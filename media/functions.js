$(function () {
	cmswebshop = CmsWebshop();
	cmswebshop.init({
		'tracking_enable': {
			'index': 'gtm:AddToCart|%ITEM_CODE%|%ITEM_TITLE%|%ITEM_CATEGORY_TITLE%|%ITEM_MANUFACTURER_TITLE%|%ITEM_PRICE%|%ITEM_CURRENCY_CODE%|%ITEM_DIFF_QTY%|%ITEM_CATEGORY%,fb:AddToCart|%ITEM_PRICE_INT%|HRK|%ITEM_ID%|product',
			'detail': 'gtm:AddToCart|%ITEM_CODE%|%ITEM_TITLE%|%ITEM_CATEGORY_TITLE%|%ITEM_MANUFACTURER_TITLE%|%ITEM_PRICE%|%ITEM_CURRENCY_CODE%|%ITEM_DIFF_QTY%|%ITEM_CATEGORY%,fb:AddToCart|%ITEM_PRICE_INT%|HRK|%ITEM_ID%|product',
			'remove': 'gtm:removeFromCart|%ITEM_CODE%|%ITEM_TITLE%|%ITEM_CATEGORY_TITLE%|%ITEM_MANUFACTURER_TITLE%|%ITEM_PRICE%|%ITEM_CURRENCY_CODE%|%ITEM_DIFF_QTY%|%ITEM_CATEGORY%',
		},
		'tracking_event': {
			'productDetailImpressions': 'productView',
			'productImpressions': 'impressions',
			'addClick': 'productClick',
		},
	});
	cmswebshop.shopping_cart.set_min_order_total();

	cmscoupon = CmsCoupon();
	cmscoupon.init();

	cmsgoogle4 = CmsGoogle4();
	cmsgoogle4.init();

	cmsparcellocker = CmsParcelLocker();
	cmsparcellocker.init({
		map_element: 'data-parcellocker_map',
		results_element: 'data-parcellocker_results',
		version: 2,
	});

	var body_class = ($('body').attr('class') || '').toString();

	if (body_class.indexOf('page-catalog-index') !== -1 && $.urlParam('search_q')) {
		var content_id = '';
		$('[data-product_id]:lt(5)').each(function () {
			content_id = content_id + $(this).data('product_id') + ',';
		});

		if (content_id.slice(0, -1)) {
			trackingEventSingle('fb', 'Search|0|HRK|' + content_id.slice(0, -1) + '|product|' + $.urlParam('search_q'));
		}
	}

	if (body_class.indexOf('page-catalog-detail') !== -1) {
		var content_id = body_class.substring(body_class.indexOf('page-catalog-detail-') + 'page-catalog-detail-'.length) + ' ';
		content_id = content_id.substring(0, content_id.indexOf(' '));
		if (content_id) {
			var content_price = $('[data-product_price]:first').text() || $('[data-product_price_loyalty]:first').text() || '0.00';
			content_price = price_2_decimal(content_price);
			trackingEventSingle('fb', 'ViewContent|' + content_price + '|HRK|' + content_id + '|product');
		}
		if ($.urlParam('search_q')) {
			trackingEventSingle('fb', 'Search|0|HRK|' + $.urlParam('search_q'));
		}
	}

	if (body_class.indexOf('page-publish-detail') !== -1) {
		var content_id = body_class.substring(body_class.indexOf('page-publish-detail-') + 'page-publish-detail-'.length) + ' ';
		content_id = content_id.substring(0, content_id.indexOf(' '));
		if (content_id) {
			trackingEventSingle('fb', 'ViewContent' + content_id + '|text');
		}
	}

	if (body_class.indexOf('page-webshop-login') !== -1) {
		trackingEventSingle('fb', 'InitiateCheckout');
	}

	if (body_class.indexOf('page-webshop-payment') !== -1) {
		trackingEventSingle('fb', 'InitiateCheckout');
		trackingEventSingle('fb', 'AddPaymentInfo');
	}

	$('.cd-header').clone(true).appendTo('.fixed-footer');

	// vars
	var scrollPosition = 0,
		body = $('body'),
		winWidth = window.parent.innerWidth,
		percentTime,
		tick,
		time = 5,
		progressBarIndex = 0,
		heroSlider = $('.slider-items'),
		ontop = $('a.ontop'),
		nlFooter = document.querySelector('.footer'),
		footerIntersection = false;

	// instashop point
	if (winWidth >= 1300) {
		$('.pd-instashop-point').on({
			mouseenter: function () {
				var id = $(this).data('id');
				$(this).addClass('active');
				$('.cp#' + id).addClass('hover');
			},
			mouseleave: function () {
				$('.cp').removeClass('hover');
				$(this).removeClass('active');
			},
		});
	} else {
		$('.pd-instashop-point').on('click', function () {
			var $this = $(this),
				id = $this.data('id');

			if ($this.hasClass('active')) {
				$('.cp').removeClass('hover');
				$this.removeClass('active');
			} else {
				$('.pd-instashop-point').removeClass('active');
				$this.addClass('active');
				$('.cp').removeClass('hover');
				$('.cp#' + id).addClass('hover');
			}
		});
	}

	if (nlFooter) {
		observer = new IntersectionObserver(entries => {
			entries.forEach(entry => {
				footerIntersection = entry.intersectionRatio > 0 ? true : false;
			});
		});

		observer.observe(nlFooter);
	}

	// fixed elements
	/*
	if (!body.hasClass('page-checkout')) {
		$(window).on('scroll', function () {
			var currScrollPosition = $(this).scrollTop();
			scrollPosition = currScrollPosition;

			if (footerIntersection) {
				body.addClass('ftr');
			} else {
				body.removeClass('ftr');
			}

			if (scrollPosition > 800) {
				ontop.addClass('active');

				if (footerIntersection) {
					body.removeClass('fixed-cd-header');
				} else {
					body.addClass('fixed-cd-header');
				}
			} else {
				ontop.removeClass('active');
				body.removeClass('fixed-cd-header');
			}

			if (!body.hasClass('page-landing')) {
				if (scrollPosition > 150) {
					body.addClass('fixed-header');
				} else {
					body.removeClass('fixed-header');
				}
			}
		});
	}
	*/

	$('.header-categories').on('click', function () {
		$(this).toggleClass('active');
		$('.categories-container').toggleClass('active');
	});

	$('.w-lang-span').on('click', function () {
		var parent = $(this).parent();

		if (parent.hasClass('active')) {
			parent.toggleClass('active');
		} else {
			$('.w-toggle').removeClass('active');
			parent.addClass('active');
		}
	});

	if (winWidth < 900) {
		$('.w-lang-mobile-close').on('click', function () {
			$(this).parent().parent().removeClass('active');
		});
	}

	// homepage slider
	if (heroSlider.length) {
		var sliderInterval = 8,
			autoplay = winWidth < 900 ? true : false;

		if (winWidth > 900) {
			heroSlider.slick({
				infinite: true,
				slidesToShow: 1,
				slidesToScroll: 1,
				fade: true,
				dots: true,
				arrows: true,
				autoplay: autoplay,
				speed: 1000,
				lazyLoad: 'ondemand',
				draggable: true,
			});

			$('.slider-nav-progress').each(function (index) {
				var progress = "<div class='inProgress inProgress" + index + "'></div>";
				$(this).html(progress);
			});

			function startProgressbar() {
				resetProgressbar();
				percentTime = 0;
				tick = setInterval(interval, sliderInterval);
			}

			function interval() {
				if (!$('.slider-item[data-slick-index="' + progressBarIndex + '"]').hasClass('slick-current')) {
					progressBarIndex = heroSlider.slick('slickCurrentSlide');
					startProgressbar();
				} else {
					percentTime += 1 / time;
					$('.inProgress' + progressBarIndex)
						.width(percentTime + '%')
						.parent()
						.parent()
						.addClass('active');
					if (percentTime >= 100) {
						heroSlider.slick('slickNext');
						progressBarIndex++;
						if (progressBarIndex > 2) {
							progressBarIndex = 0;
						}
						startProgressbar();
					}
				}
			}

			function resetProgressbar() {
				$('.inProgress')
					.width(0 + '%')
					.parent()
					.parent()
					.removeClass('active');
				clearInterval(tick);
			}
			startProgressbar();

			//pause on hover
			$('.slider-item')
				.mouseenter(function (ev) {
					clearInterval(tick);
				})
				.mouseleave(function () {
					tick = setInterval(interval, sliderInterval);
				});
		}
	}

	$('.slider-nav-item').click(function () {
		if (!$(this).hasClass('active')) {
			clearInterval(tick);
			var goToThisIndex = $(this).data('slide-index');
			heroSlider.slick('slickGoTo', goToThisIndex, false);
			startProgressbar();
		}
	});

	// catalog image slider
	$('.cd-hero-slider').slick({
		infinite: false,
		slidesToShow: 1,
		slidesToScroll: 1,
		fade: true,
		dots: false,
		arrows: false,
		draggable: false,
		lazyLoad: 'ondemand',
		responsive: [
			{
				breakpoint: 950,
				settings: {
					fade: false,
				},
			},
		],
	});

	$('.cd-thumb').on('click', function () {
		var $this = $(this),
			index = $this.data('slide-index');

		$('.cd-hero-slider').slick('slickGoTo', index);
		$this.addClass('active').siblings().removeClass('active');
	});

	$('.cd-hero-slider').on('afterChange', function (event, slick, direction) {
		$('.cd-thumb[data-slide-index="' + direction + '"]')
			.addClass('active')
			.siblings()
			.removeClass('active');
	});

	$('.cd-phone-order, .loyalty-cnt').on({
		'mouseenter': function () {
			$(this).addClass('tooltip-active');
		},
		'mouseleave': function () {
			$(this).removeClass('tooltip-active');
		},
	});

	//catalog availability flyout
	let flyout = $('.flyout');
	if (flyout.length) {
		$(document).ready(function () {
			$(document).on('click', '.cd-flyout-btn', function () {
				let flyoutBtnDataValue = $(this).attr('data-flyout_code');

				$('body').addClass('flyout-active');
				let flyoutBtnData = $('.flyout[data-flyout="' + flyoutBtnDataValue + '"]');
				flyoutBtnData.addClass('active');
			});

			$(document).on('click', '.cd-flyout-close', function () {
				close_flyout(this);
			});
		});

		function close_flyout() {
			$('.flyout').removeClass('active');
			$('body').removeClass('flyout-active');
		}
	}

	$('.cd-store-title').on('click', function () {
		$(this).parent().toggleClass('active');
	});

	// preview coupons
	$(document).on('click', '.ww-preview-coupons-label', function () {
		$(this).parent().toggleClass('preview-active');
	});

	// testimonials
	$('.ts-items').slick({
		infinite: true,
		slidesToShow: 1,
		slidesToScroll: 1,
		fade: true,
		dots: false,
		arrows: true,
		lazyLoad: 'ondemand',
		draggable: false,
	});

	// about images slider
	$('.about-images').slick({
		infinite: true,
		slidesToShow: 1,
		slidesToScroll: 1,
		fade: true,
		dots: false,
		arrows: true,
		lazyLoad: 'ondemand',
		draggable: false,
	});

	// buttons
	$('.btn, button').each(function () {
		var $this = $(this);

		if (!$this.find('>span').length) {
			$this.wrapInner('<span/>');
		}
	});

	if (winWidth > 1300) {
		$('.categories-widget>li').on({
			'mouseover': function () {
				$(this).addClass('active');
			},
			'mouseleave': function () {
				$(this).removeClass('active');
			},
		});
	} else {
		$('.categories-widget>li>a').on('click', function (e) {
			e.preventDefault();
			var parent = $(this).parent();
			if (winWidth > 900) {
				if (parent.hasClass('active')) {
					parent.toggleClass('active');
				} else {
					$('.categories-widget>li').removeClass('active');
					parent.addClass('active');
				}
			} else {
				parent.toggleClass('active');
			}
		});
	}

	if (winWidth >= 1300) {
		$('.nav-categories a').on('mouseover', function () {
			toggleCatItems($(this));
		});
	}
	if (winWidth < 1300 && winWidth > 900) {
		$('.nav-categories a').on('click', function (e) {
			if ($(this).parent().hasClass('has-children')) {
				e.preventDefault();
				toggleCatItems($(this));
			}
		});

		$('.nav a').on('click', function (e) {
			var parent = $(this).parent();
			if (!parent.hasClass('active')) {
				$('.nav li').removeClass('active');
			}
			if (parent.hasClass('has-children')) {
				e.preventDefault();
				parent.toggleClass('active');
			}
		});
	}

	function toggleCatItems(el) {
		var $this = el,
			parent = $this.parent(),
			id = $this.data('category'),
			categoryItems = $('.nav-categories-right[data-category="' + id + '"]');

		parent.addClass('active').siblings().removeClass('active');
		categoryItems.addClass('active').siblings().removeClass('active');
	}

	// mobile nav
	var windowOffset = 0;
	$('.btn-toggle-nav').on('click', function () {
		if (!body.hasClass('active-nav')) {
			windowOffset = $(window).scrollTop();
			body.addClass('active-nav');
		} else {
			setTimeout(function () {
				window.scroll(0, windowOffset);
			});
			body.removeClass('active-nav');
		}
		$(this).toggleClass('active');
	});

	if (winWidth <= 900) {
		$('.nav-categories-right').removeClass('active');

		var menuTitle = $('.m-nav-title'),
			initialMenuTitle = menuTitle.text();

		$('.nav-categories a').on('click', function (e) {
			var $this = $(this),
				parent = $this.parent(),
				id = $this.data('category'),
				navTitle = $this.text();

			if (parent.hasClass('has-children')) {
				e.preventDefault();
				$('.categories-container').addClass('lvl2');
				$('.nav-categories-right[data-category=' + id + ']').addClass('active');
				menuTitle.text(navTitle);
				menuTitle.addClass('active');
			}
		});

		$('.m-nav-title').on('click', function () {
			$(this).removeClass('active');
			$('.nav-categories-right').removeClass('active');
			$('.categories-container').removeClass('lvl2');
			menuTitle.text(initialMenuTitle);
		});
	}

	cmswishlist = CmsWishlist();
	cmswishlist.init({
		'tracking_enable': {
			'index': 'fb:AddToWishlist|%ITEM_PRICE%|HRK|%ITEM_ID%|product,',
			'detail': 'fb:AddToWishlist|%ITEM_PRICE%|HRK|%ITEM_ID%|product',
			'remove': 'fb:removeFromWishlist|%ITEM_PRICE%|HRK|%ITEM_ID%|product',
		},
	});

	// image titles
	$('.main-content .image-border').CmsUtilsImageTitle();

	// search autocomplete
	const allProductsLabel = $('.autocomplete-container').data('show-products');
	$("input[name='search_q']").CmsAutocomplete({
		module: 'catalog',
		advanced: true,
		separated_contenttype: true,
		show_all: true,
		lang: site_lang,
		result_image: '60x60_r',
		result_fields: 'all:image,price,category_title',
		result_per_page: 'catalogproduct:5,4',
		layout_extra: {
			'catalogproduct': '<span class="search-image"><img src="%item_image%" alt=""></span><span class="search-col"><span class="search-category">%item_category_title%</span><span class="search-title">%item_label%</span><span class="search-price">%item_price%</span></span></span>',
			'publish.01': '<span>%item_label%<span>',
			'publish.02': '<span>%item_label%<span>',
		},
		show_all_layout_extra: {
			'catalogproduct': '<span>%item_label%</span>',
			'catalogcategory': '%item_label%',
			'catalogmanufacturer': '%item_label%',
		},
		show_all_label_extra: {
			'catalogproduct': '<span>' + allProductsLabel + ' (%item_total%)</span>',
			'catalogcategory': 'Prikaži sve',
			'catalogmanufacturer': 'Prikaži sve brandove',
			'publish.01': 'Prikaži sve članke',
			'publish.02': 'Prikaži sve recepti',
		},
		auto_redirect: true,
	});

	// share
	$(document).CmsShare({networks: 'facebook,whatsapp,viber,email'});

	// newsletter
	cmsnewsletter = CmsNewsletter();

	var nlWidth = 900,
		nlHeight = 420;

	if (winWidth < 950) {
		nlWidth = 600;
		nlHeight = 370;
	}
	var newsletter_leaving = $('form[data-newsletter_leaving]');
	if (newsletter_leaving.size() && newsletter_leaving.data('newsletter_leaving')) {
		cmsnewsletter.detect_leaving('newsletter_popup', 1, 0, nlWidth, nlHeight, newsletter_leaving.data('newsletter_leaving_ignores'));
	}

	cmsfeedback = CmsFeedback();
	cmsfeedback.init();

	// autocomplete locations
	$('#field-location').live('focus', function () {
		cmswebshop.shopping_cart.location_choice($(this).attr('name'), true);
	});

	$(document).on('click', '.cp-btn-addtocart, .toggle-cart, .cd-btn-add', function () {
		windowOffset = $(window).scrollTop();
	});

	$('.ww-preview').height($(window).height());
	$(document).on('click', '.toggle-cart', function () {
		$('.ww-preview').height($(window).height());
		if (!body.hasClass('active-cart')) {
			body.addClass('active-cart');
		} else {
			if (winWidth < 900) {
				setTimeout(function () {
					window.scroll(0, windowOffset);
				});
			}
			body.removeClass('active-cart');
		}
		$('.ww-preview').toggleClass('active');
	});

	$(document).on('click', '.ww-preview-close', function () {
		body.removeClass('active-cart');
		body.removeClass('active-cart-modal');
		body.removeClass('active');
		$('.ww-preview').removeClass('active');
		if (winWidth < 900) {
			setTimeout(function () {
				window.scroll(0, windowOffset);
			});
		}
	});

	//Close active element
	function closeActiveElement(el, cls) {
		$(document).on('mouseup', function (e) {
			var container = el;
			cls = cls ? cls : 'active';
			if (!container.is(e.target) && container.has(e.target).length === 0) {
				container.removeClass(cls);
				if (!$('.flyout').hasClass('active')) {
					body.removeClass('flyout-active');
				}
			}
		});
	}
	closeActiveElement($('.aw'));
	closeActiveElement($('.categories-container, .header-categories'));
	closeActiveElement($('.ww-preview'));
	closeActiveElement($('.nav li'));
	closeActiveElement($('.w-lang'));
	closeActiveElement($('.flyout'));

	// shipping address
	var shippingAddressEl = $('.shipping-location-address');
	function updateShippingAddress(el) {
		var id = el.val(),
			addressEl = $('.shipping-location-address[data-id="' + id + '"]');

		shippingAddressEl.removeClass('active');
		addressEl.addClass('active');
	}
	$(document).on('change', '#field-shipping_pickup_location', function () {
		updateShippingAddress($(this));
	});

	if ($('#field-shipping_pickup_location').length) {
		updateShippingAddress($('#field-shipping_pickup_location'));
	}

	// catalog filter
	$('#attribute_filters_select').CmsFilter({lang: site_lang});

	//locations
	var location_map = $('#map_canvas');
	if (location_map.size()) {
		setTimeout(function () {
			cmslocation = CmsLocation();
			cmslocation.config.markeractive = '/media/images/icons/pin1.svg';
			cmslocation.config.markerinactive = '/media/images/icons/pin1.svg';
			cmslocation.config.scrollwheel = false;
			cmslocation.init(site_lang, {maxZoom: 30}, {maxWidth: 200, pixelOffset: new google.maps.Size(10, -155), closeBoxMargin: '0', closeBoxURL: '/media/images/icons/close.svg'});
			cmslocation.set_points_detect();
		}, 2000);
	}

	var scrollPosition = 0;

	$(document).on('mouseup', function (e) {
		var container = $('div.modal-box');

		if (!container.is(e.target) && container.has(e.target).length === 0) {
			$('div.product-message-modal').removeClass('active');
		}
	});

	// thank you page show password
	if ($('#field-password').size()) {
		$('#field-password').showPassword();
	}

	// floating labels
	//$('.field-b_company_name, .field-b_company_oib, .field-b_company_address').addClass('field-cnt').removeClass('field');

	$('.field-b_r1').on('click', function () {
		setTimeout(function () {
			$('.field-cnt').floatingFormLabels();
		}, 200);
	});

	if ($('.field').length) {
		$('.field').floatingFormLabels();
	}

	setTimeout(function () {
		if (winWidth > 900) {
			$('.wc-form:not(".step3") .field:first').addClass('ffl-floated');
		} else {
			$('.wc-form:not(".step3") .field:first input').blur();
		}
	}, 100);

	// autosize textareas
	$('textarea').autosize({append: '\n'});

	// related products
	if (winWidth > 900) {
		$('.cd-rp-slider').slick({
			slidesToShow: 5,
			slidesToScroll: 5,
			infinite: false,
			arrows: true,
			draggable: false,
			lazyLoad: 'ondemand',
		});

		$('.fwd-slider').slick({
			slidesToShow: 4,
			slidesToScroll: 4,
			infinite: false,
			arrows: true,
			draggable: false,
			lazyLoad: 'ondemand',
			responsive: [
				{
					breakpoint: 1300,
					settings: {
						slidesToShow: 3,
						slidesToScroll: 3,
					},
					breakpoint: 950,
					settings: {
						slidesToShow: 4,
						slidesToScroll: 4,
					},
				},
			],
		});
	}

	// instashop
	var gmargins = 0;
	(instaWidth = 1480), (instaHeight = 'auto'), (instaAutoSize = false);

	function openInstashop() {
		var winWidth = window.parent.innerWidth;

		if (winWidth < 1670) {
			instaWidth = 1170;
		}
		if (winWidth < 1400) {
			instaWidth = 870;
		}
		if (winWidth < 1100) {
			instaWidth = 600;
		}

		if (winWidth > 1100) {
			$('.instashop-gallery').fancybox({
				'type': 'iframe',
				'padding': 0,
				'margin': gmargins,
				'scrolling': 'auto',
				'width': instaWidth,
				'height': instaHeight,
				'autoSize': instaAutoSize,
				'autoScale': false,
				'fitToView': false,
				'autoCenter': true,
				'wrapCSS': 'fancybox-instashop',
				'prevEffect': 'none',
				'nextEffect': 'none',
				'openEffect': 'none',
				//'arrows': false,
				afterLoad: function (current, previous) {
					$('body').addClass('instashop-active');
				},
				afterClose: function () {
					$('body').removeClass('instashop-active');
				},
			});
		}

		if (winWidth > 900 && winWidth < 950) {
			$('.pd-instashop-items .cp').removeClass('cp-list');
		}
	}
	openInstashop();

	window.addEventListener(
		'orientationchange',
		function () {
			openInstashop();
		},
		false
	);

	// accept terms error
	var acceptTerms = $('#field-accept_terms');

	function checkTerms() {
		if (!acceptTerms.is(':checked')) $('.wc-accept-terms-tooltip1').toggleClass('active');
	}

	$('.btn-finish').on('mouseover', function () {
		checkTerms();
	});
	$('.btn-finish').on('mouseout', function () {
		checkTerms();
	});

	$('.cart_info_item_count').bind('DOMSubtreeModified', function () {
		var cartTotal = $('.cart_info_item_count').text();

		setTimeout(function () {
			if (cartTotal == 0 || !cartTotal) {
				$('.ww-preview-items').addClass('empty');
				$('.ww-preview-buttons').addClass('active');
			} else {
				$('.ww-preview-items').removeClass('empty');
				$('.ww-preview-buttons').removeClass('active');
			}
		}, 100);
	});

	if (winWidth > 900) {
		$('.aw-login').on('click', function (e) {
			e.preventDefault();
			$('.aw').toggleClass('active');
		});
	}

	$('.btn-toggle-filter, .cf-filter-close').on('click', function () {
		body.toggleClass('active-filter');
	});

	$('.sw-toggle').on('click', function () {
		$('.sw').toggleClass('active');
		$('.sw-input').focus();
	});

	if (winWidth <= 950) {
		$('.cf-recipes .cf-title').on('click', function () {
			var parent = $(this).parent();
			parent.toggleClass('active');
		});
	}

	$('.bottom-title').on('click', function () {
		$(this).parent().toggleClass('active');
	});

	if (winWidth > 950) {
		$('.p-recipes-main').css('min-height', $('.cf').outerHeight() - 180);
	}

	if ($('.ln-nav-container').length) {
		body.addClass('has-menu');
	}

	//comments stars
	$('.comment-rate-item').hover(function () {
		$(this).prevAll('.comment-rate-item').andSelf().toggleClass('active-hover');
	});

	$('.btn-load-more-comments').on('click', function () {
		$('.comments-list').addClass('active');
	});

	// tabs
	$('.m-tabs').CmsUtilsTabs({
		animationType: 'css',
	});

	// faq
	$('.fp-title').on('click', function () {
		$(this).parent().toggleClass('active');
	});

	if (body.hasClass('page-checkout')) {
		$('.safe-purchase').insertAfter('.footer-col3');
	}

	//Hellobar
	if ($('.hello-bar').length) {
		$('body').addClass('hello-bar-active');
	}

	/* var helloBar_cookie = 'hellobar',
		helloBar = $('.hello-bar');

	if (helloBar.length) {
		$('.heb-close').on('click', function () {
			helloBar.hide();
			$('body').removeClass('hello-bar-active');
			$.cookie(helloBar_cookie, 1, {expires: 1, path: '/'});
		});

		if ($.cookie(helloBar_cookie) == 0 || $.cookie(helloBar_cookie) == undefined) {
			helloBar.show();
		} else if ($.cookie(helloBar_cookie) == 1) {
			helloBar.hide();
			$('body').removeClass('hello-bar-active');
		}
	} */

	$('[data-countdown]').each(function () {
		var $this = $(this),
			finalDate = $(this).data('countdown'),
			time = '';
		$this.countdown(finalDate, function (event) {
			if (event.offset.days > 0) {
				time = '<div class="day"><span>%D</span>d</div><div class="hour"><span>%H</span>h</div><div class="min"><span>%M</span>m</div><div class="sec"><span>%S</span>s</div>';
			} else {
				time = '<div class="hour"><span>%H</span>h</div><div class="min"><span>%M</span>m</div><div class="sec"><span>%S</span>s</div>';
			}
			$this.html(event.strftime(time));
		});
	});

	setTimeout(function () {
		var $hellobarListData = $('.hellobar-coupon-btn').data('list_link');
		if ($('.hello-bar').length) {
			if ($hellobarListData.length > 0) {
				if (!$('body').hasClass('page-shopping-cart')) {
					if (!$('.hello-bar').hasClass('active')) {
						$('.hellobar-coupon-btn').on('click', function () {
							var $hellobarListLink = $(this).data('list_link');
							setTimeout(function () {
								location.replace($hellobarListLink);
							}, 3000);
						});
					}
				}
			}
		}
	}, 1000);

	ssm.addState({
		query: '(max-width: 1300px)',
		onEnter: function () {
			$('.page-contact .map').insertAfter('.contact-row');
			$('.page-catalog .c-items:not(.c-special-items)').addClass('c-items3').removeClass('c-items4');
		},
		onLeave: function () {
			$('.page-contact .map').insertAfter('.main-wrapper');
			$('.page-catalog .c-items:not(.c-special-items)').addClass('c-items4').removeClass('c-items3');
		},
	});

	ssm.addState({
		query: '(max-width: 950px)',
		onEnter: function () {
			$('.pw-btns').detach().appendTo('.pw-recipes-col1');
			$('.pd-recipe-related-products').insertAfter('#comments');
			$('.ww-coupons-cart, .free-delivery-container').appendTo('.w-col2-cnt-top');
			$('.qo-col-cnt-desc').insertBefore('.qo-col1');
			$('.wishlist-items').addClass('c-items3').removeClass('c-items5');
			$('.m-logo').insertBefore('.c-title');
			$('.auth-box-loyalty').detach().appendTo('.main-content');
			$('.a-dashboard-title').detach().prependTo('.main-content');
			$('.cf-item').removeClass('active');
			$('.cf-active').insertBefore('#items_catalog_layout');
			$('.page-recipes .cf-active').detach().appendTo('.p-recipes-header');
			$('.ww-coupons-preview').insertAfter('.ww-preview-header');
		},
		onLeave: function () {
			$('.pw-btns').insertAfter('.pw-recipe-items');
			$('.pd-recipe-related-products').insertAfter('.pd-recipe-sidebar-top');
			$('.ww-coupons-cart').insertBefore('.cart-totals');
			$('.free-delivery-container').insertAfter('.w-btn-finish');
			$('.qo-col-cnt-desc').insertBefore('.qo-col-cnt-totals');
			$('.wishlist-items').addClass('c-items5').removeClass('c-items3');
			$('.m-logo').insertBefore('.cf-products');
			$('.auth-box-loyalty').detach().appendTo('.sidebar');
			$('.a-dashboard-title').detach().prependTo('.a-sidebar-cnt');
			$('.page-recipes .cf-active').detach().prependTo('.cf-body');
		},
	});

	ssm.addState({
		query: '(max-width: 900px)',
		onEnter: function () {
      		/*if(!$('body').hasClass('page-error-404')){
				$('.sidebar-cnt').insertBefore('#newsletter');
			}*/
			$('.bottom-title').append('<span class="toggle-icon"/>');
			$('.benefits').insertAfter('.categories');
			$('.pw-btns').insertAfter('.pw-recipes-col2');
			$('.page-homepage .sw').appendTo('.sw-placeholder');
			$('.categories-container').insertBefore('.m-nav-support');
			$('.nav').insertBefore('.m-nav-support');
			$('.nav-categories li').removeClass('active');
			$('.nav-categories').prepend('<li class="m-cat-item">');
			$('.quick-order').appendTo('.m-cat-item');
			$('.w-lang').prependTo('.categories-container');
			$('.about-images').appendTo('.about-image-placeholder');
			$('.ld-images').insertAfter('.ld-contact');
			$('.pd-header').insertAfter('.header');
			$('.pd-info').insertBefore('.pd-title');
			$('.pd-desc').before('<div class="pd-info-feedback"/>');
			$('.pd-info-link').appendTo('.pd-info-feedback');
			$('.cf-item').removeClass('active');
			$('.pd-recipe-header-wrapper').insertAfter('.header');
			$('.pd-recipe-info').detach().appendTo('.pd-recipe-info-container');
			$('.pd-recipe-sidebar').appendTo('.pd-recipe-sidebar-m');
			$('.page-catalog-level0 .c-counter').insertAfter('.c-title');
			$('.cd-m-header:first').appendTo('.cd-m-header-placeholder');
			$('.cd-attr-container').detach().prependTo('.cd-tab-description');
			$('.cd-related').detach().appendTo('.cd-col2');
			$('.w-col .free-delivery-container').detach().appendTo('.w-col2-cnt');
			$('.a-dashboard-title').detach().prependTo('.sidebar');
			$('.page-checkout .header-contact').insertAfter('.footer-col3');
			$('.wc-col-shipping').detach().prependTo('.wc-step2-col1');
			$('.wc-col-totals').insertAfter('.ww-cart-header');
			$('.free-delivery-container').insertBefore('.ww-cart-btns');
			$('.w-btn-change').insertAfter('.btn-toggle-cart');
			$('.step3 .btn-toggle-cart, .step3 .ww-cart-items, .step4 .btn-toggle-cart, .step4 .ww-cart-items').addClass('active');
			$('.exchange-note').insertAfter('.footer-col3');
		},
		onLeave: function () {
			if (!$('body').hasClass('quick')) {
				location.reload();
			}
		},
	});

	$('[data-checkout_country_selector]').on('change', function () {
		var selectedCountry = $(this).find('option:selected').val();
		setShippingCountry(selectedCountry);
		//$('body').addClass('body-country-select');
	});

	$('[data-shipping_country_select_el]').on('change', function () {
		var selectedCountry = $(this).find('option:selected').val();
		setShippingCountry(selectedCountry);
	});

	var extraitem_priority = $('input[name="extraitem_priority"]');
	var input = $('div[id^="extraitem-"]').find('input');

	var id = input.data('id');
	if (extraitem_priority.size()) {
		extraitem_priority.live('change', function () {
			if ($(this).is(':checked')) {
				cmswebshop.shopping_cart.set_customer_data('extraitems', id);
				setTimeout(function () {
					$('input[name="extraitem_priority"]').attr('checked', 'checked');
				}, 400);
			} else {
				cmswebshop.shopping_cart.set_customer_data('extraitems', 'remove-' + id);
				setTimeout(function () {
					$('input[name="extraitem_priority"]').removeAttr('checked');
				}, 400);
			}
		});
	}
	$('body ul.tabs li').on('click', function () {
		if ($(this).hasClass('active')) {
			gtmReload($(this).children('a').attr('href'));
		}
	});

	$('input[name="prepared_cash"]').change(function () {
		if ($(this).is(':checked')) {
			$('[data-cash_amount_input]').show();
		} else {
			$('[data-cash_amount_input]').hide();
		}
	});
	$('input[name="scheduled_dropoff"]').change(function () {
		if ($(this).is(':checked')) {
			$('[data-delivery_time_input]').show();
		} else {
			$('[data-delivery_time_input]').hide();
		}
	});

	let selectHour = $('[data-wolt_time_picker]');
	let stepMinute = 5;
	let storedTime = $('input[name="scheduled_dropoff_time"]').val() || '10:30';

	$(document).ready(function () {
		$.datepicker.setDefaults($.datepicker.regional[site_i18n_lang]);
		let selectedDate = $('[data-wolt_date_picker]').find(':selected');
		let openingHour = parseInt($(selectedDate).attr('data-date_opening_hour'));
		let closingHour = parseInt($(selectedDate).attr('data-date_closing_hour')) - 1;
		let minuteMin = $('[data-wolt_date_picker]').find('option:first-child').attr('data-date_opening_minute');
		let minuteMax = $('[data-wolt_date_picker]').find('option:first-child').attr('data-date_closing_minute');

		selectHour
			.timepicker({
				timeOnlyTitle: $('.select-time-title').html(),
				hourMin: openingHour,
				hourMax: closingHour,
				minuteMin: minuteMin,
				minuteMax: minuteMax,
				stepMinute: stepMinute,
				defaultValue: storedTime,
				controlType: 'select',
				beforeShow: function () {
					setMinDeliveryTime(new Date(), stepMinute);
					$('.sw-start-date-datepicker').append($('#ui-datepicker-div'));
				},
			})
			.attr('readonly', 'readonly');
	});

	/* $('.delivery-time-input .input-wrapper').click(function(){
		$('.ui-datepicker').addClass('active-timepicker');
	});
	$('button.ui-priority-primary').click(function() {
		$('.ui-datepicker').removeClass('active-timepicker');
	});
	closeActiveElement($('.ui-datepicker, .active-timepicker')); */

	$(document).on('change', '.ui-timepicker-select', function () {
		setMinDeliveryTime(new Date(), stepMinute);
	});

	function getCurrentDate() {
		let currentDate = new Date();
		let currentYear = currentDate.getFullYear();
		let currentDay = currentDate.getDate();
		let currentMonth = currentDate.getMonth() + 1;
		currentDay = currentDay < 10 ? `0${currentDay}` : currentDay;
		currentMonth = currentMonth < 10 ? `0${currentMonth}` : currentMonth;
		let currentFullDate = `${currentYear}-${currentMonth}-${currentDay}`;

		return currentFullDate;
	}

	function setMinDeliveryTime(date, stepMinute) {
		let selectedHour = $('.ui-timepicker-select').find(':selected').val() || false;
		if ($.datepicker._curInst !== null) {
			selectedHour = $.datepicker._get($.datepicker._curInst, 'timepicker').hour || selectedHour;
		}
		let selectedDate = $('[data-wolt_date_picker]').find(':selected');
		let minHour = parseInt($(selectedDate).attr('data-date_opening_hour'));
		let maxHour = parseInt($(selectedDate).attr('data-date_closing_hour'));
		let currentFullDate = getCurrentDate();
		let minMinuteFinal = 0;
		let maxMinuteFinal = 55;

		//initial load for selected date
		if (selectedHour == minHour || !selectedHour) {
			minMinuteFinal = parseInt($(selectedDate).attr('data-date_opening_minute'));
		}

		if (selectedHour == maxHour) {
			maxMinuteFinal = parseInt($(selectedDate).attr('data-date_closing_minute'));
		}

		//recalculate min hours and minutes based on current time and delivery time estimate
		if (selectedDate.val() == currentFullDate) {
			let totalPostponement = 90;
			let currentDate = date;
			if (currentDate.getHours() < minHour) {
				currentDate.setHours(minHour);
			}
			let minScheduleTime = currentDate.setMinutes(currentDate.getMinutes() + totalPostponement);
			let minScheduleDate = new Date(minScheduleTime);

			//set min minute to nearset 5 in future
			minHour = minScheduleDate.getHours();
			let minMinuteInitial = minScheduleDate.getMinutes();
			let minMinuteRounded = Math.round(minMinuteInitial / stepMinute) * stepMinute;
			minMinuteFinal = minMinuteRounded < minMinuteInitial ? minMinuteRounded + stepMinute : minMinuteRounded;
			selectedHour = selectedHour < minHour || selectedHour > maxHour ? $('.ui-timepicker-select').find('option:first-child').val() : selectedHour;

			//autoselect next date option if current time is greater than last option for today
			if (minHour >= maxHour) {
				let nextAvailableDate = $('[data-wolt_date_picker] option[value="' + selectedDate.val() + '"]')
					.next()
					.val();
				$('[data-wolt_date_picker]').val(nextAvailableDate).trigger('change');
				minHour = parseInt($(selectedDate).attr('data-date_opening_hour'));
				minMinuteFinal = 0;
			}

			if (selectedHour != $('.ui-timepicker-select').find('option:first-child').val()) {
				minMinuteFinal = 0;
			}
		}

		if (parseInt(minMinuteFinal) == 60) {
			minMinuteFinal = 0;
			minHour += 1;
		}

		if (selectedHour == maxHour) {
			minMinuteFinal = 0;
		}

		selectHour.timepicker('option', 'minuteMin', minMinuteFinal);
		selectHour.timepicker('option', 'minuteMax', maxMinuteFinal);
		selectHour.timepicker('option', 'hourMin', minHour);
		selectHour.timepicker('option', 'hourMax', maxHour);
	}

	$('select[data-shipping_pickup_location]').on('change', function () {
		cmswebshop.shopping_cart.set_customer_data('shipping_pickup_location', $(this).val());
		setTimeout(function () {
			location.reload();
		}, 250);
	});

	$('[data-shipping_country_select_btn]').on('click', function () {
		closeCountrySelectModal();
	});
});

$(window).load(function () {
	var cdHeader = $('.cd-header');
	$('.cd-header-placeholder').height(cdHeader.outerHeight());
});

// recaptcha v3
var submited_form_id = '';
$('[data-before_submit]').click(function () {
	submited_form_id = $(this).closest('form').attr('id');
});
function onSubmit(token) {
	document.getElementById(submited_form_id).submit();
}

function closeCountrySelectModal() {
	$('[data-country_select_alert_main_el]').hide();
	$('[data-shipping_country_box]').hide();
	$('body').removeClass('body-country-select');
}

function openCountrySelectModal() {
	var selectedCountry = $('[data-shipping_country_select_el]').find(':selected').val();
	setShippingCountry(selectedCountry);
	setTimeout(function () {
		$('[data-country_select_alert_main_el]').show();
		$('[data-shipping_country_box]').show();
		$('body').addClass('body-country-select');
		$('.w-lang').removeClass('active');
	}, 500);
}

function setShippingCountry(selectedCountry, closeModal = false) {
	$.post(
		'/api/webshop/set_shipping_country/',
		{
			country: selectedCountry,
		},
		function (data) {
			if (data.response === 'ok') {
				//setTimeout(function () {
				$('[data-country_select_alert_main_el]').show();
				//}, 500);

				if (data.unavailable_items.length > 0) {
					$('body').addClass('hidden-close-btn');
					let unavailable_items = data.unavailable_items;

					if ($('body').hasClass('page-webshop-shipping')) {
						$('body').addClass('body-country-select');
					}

					$('[data-shipping_country_select_btn]').hide();
					$('[data-country_change_response_el]').hide();
					$('[data-country_change_response_el]').fadeIn(500);
					$('[data-unavailable_items_el]').html('');
					$.each(data.unavailable_items_html, function (cart_code, html) {
						$('[data-unavailable_items_el]').append(html);
					});
					let unavailable_items_string = unavailable_items.join(',');
					let unavilable_items_btn_href = 'javascript:cmswebshop.shopping_cart.remove_all("' + unavailable_items_string + '");';
					$('[data-unavailable_items_remove_btn]').attr('href', unavilable_items_btn_href);
				} else {
					$('body').removeClass('hidden-close-btn');
					$('[data-country_change_response_el]').hide();
					$('[data-shipping_country_select_btn]').show();

					if ($('body').hasClass('page-webshop-shipping')) {
						location.reload();
						$('body').removeClass('body-country-select');
					}

					if (closeModal) {
						var selectedOption = $('[data-shipping_country_select_el]').find(':selected');
						selectedOption.removeAttr('selected');
						$('[data-shipping_country_select_el]').find("option[value='1']").attr('selected', 'selected');
						closeCountrySelectModal();
					}
				}

				if (data.country_title) {
					$('[data-selected_country]').html(data.country_title);
					//if ($('body').hasClass('page-catalog-detail')) {
					if (data.country_title == 'Hrvatska') {
						$('[data-selected_country-croatia]').hide();
						$('[data-selected_country-croatia-adtc]').show();
						$('[data-selected_country-croatia-adtc]').removeClass('pickup-not-visible');
					} else {
						$('[data-selected_country-croatia]').show();
						$('[data-selected_country-croatia-adtc]').hide();
						$('[data-selected_country-croatia-adtc]').addClass('pickup-not-visible');
					}
					//}
				}
			} else {
			}
		},
		'json'
	);
}


$(document).ready(function(){
	var targetNode = document.querySelector('[data-country_select_alert_main_el]');
	if (targetNode) {
		var observer = new MutationObserver(function(mutationsList, observer) {
			if ($(targetNode).is(':visible')) {
				setTimeout(function(){
					$('[data-unavailable_items_remove_btn]').css('pointer-events', 'auto');
				}, 3000);
				observer.disconnect();
			}
		});
		var config = { attributes: true, childList: true, subtree: true };
		observer.observe(targetNode, config);
	}
});

$('.shipping-country-close').on('click', function () {
	$('body').removeClass('body-country-select');
	$('.country-select-modal').hide();
});
