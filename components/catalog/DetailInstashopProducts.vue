<template>
	<BaseCatalogProductsWidget :fetch="{id: itemsIds}" v-slot="{items: products}">
		<div class="c-items pd-instashop-items" v-if="products?.length">
			<CatalogIndexEntry v-for="product in products" :key="product.id" :item="product" list="" class="cp-list" mode="instashop" />
		</div>

		<WebshopInstashopAddAll :items="products" />
	</BaseCatalogProductsWidget>
</template>

<script setup>
	const props = defineProps(['items']);
	const itemsIds = [];
	if(props?.items){
		props.items.forEach(item => {
			itemsIds.push(item.id);
		});
	}
</script>
