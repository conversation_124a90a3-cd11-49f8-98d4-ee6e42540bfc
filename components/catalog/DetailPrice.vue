<template>
	<div class="cd-price-container">
		<div class="cd-price" v-if="b2b">
			<div class="cd-current-price"><BaseUtilsFormatCurrency :price="item.price_b2b_custom" /></div>
		</div>
		<template v-else>
			<WebshopLoyalty v-slot="{onSubmit, loading, newIsActive, loyalty}">
				<template v-if="loyalty?.active">
					<div class="cd-loyalty-price">
						<BaseCmsLabel code="loyalty_club" tag="div" class="cd-loyalty-club" />
						<div class="cd-price">
							<div class="cd-current-price red" v-if="item.discount_percent > loyalty.discount_percent"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
							<div class="cd-current-price red" v-else><BaseUtilsFormatCurrency :price="(item.basic_price_custom * (1 - (loyalty.discount_percent  / 100)))" /></div>

							<span class="cd-discount-badge">-{{loyalty.discount_percent}}%</span>
							<div class="cd-save">
								<BaseCmsLabel code="price_save" tag="span" class="label" />
								<BaseUtilsFormatCurrency :price="(item.basic_price_custom - (item.basic_price_custom * (1 - (loyalty.discount_percent  / 100))))" />
							</div>
							<div class="cd-old-price">
								<span><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></span>
							</div>
						</div>
						<BaseCmsLabel code="tax_included" tag="div" class="cd-tax" />

						<div class="cd-tax cd-lowest-price" v-if="item?.extra_price_lowest && item.extra_price_lowest > 0 && item.discount_percent_custom > 0 || item.price_custom < item.basic_price_custom">
							<BaseCmsLabel code="lowest_price" />:
							<BaseUtilsFormatCurrency :price="item.extra_price_lowest" />
						</div>
					</div>
				</template>
				<template v-else>
					<div class="cd-price">
						<template v-if="item.discount_percent_custom > 0 || item.price_custom < item.basic_price_custom">
							<div class="cd-old-price">
								<span><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></span>
							</div>
							<div class="cd-current-price red"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
							<span class="cd-discount-badge">-{{item.discount_percent}}%</span>
							<div class="cd-save"><BaseCmsLabel code="price_save" tag="span" class="label" /> <BaseUtilsFormatCurrency :price="item.basic_price_custom - item.price_custom" /></div>
						</template>
						<template v-else>
							<div class="cd-current-price"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
						</template>
					</div>
					<BaseCmsLabel code="tax_included" tag="div" class="cd-tax" />
					<div class="cd-tax cd-lowest-price" v-if="item.extra_price_lowest && item.extra_price_lowest > 0 && item.discount_percent > 0 ||  item.price_custom < item.basic_price_custom"><BaseCmsLabel code="lowest_price" />: <BaseUtilsFormatCurrency :price="item.extra_price_lowest" /></div>
				</template>
			</WebshopLoyalty>
		</template>
	</div>
</template>

<script setup>
	const props = defineProps(['item', 'b2b']);
</script>
