<template>
	<div class="ci-promo-container" v-if="items?.length || promos?.length">
		<div class="wrapper">
			<div class="ci-promo-items" v-if="promos?.length">
				<template v-for="promo in promos" :key="promo.id">
					<component :is="promo.link ? NuxtLink : 'div'" :to="promo.link ? promo.url_without_domain : undefined" class="ci-promo-item" :class="[promo.link ? 'ci-promo-item-link' : '', promo.template]">
						<template v-if="!mobileBreakpoint">
							<BaseUiImage :data="promo.image_thumbs?.['width1480-height280-crop1']" alt="" default="/images/no-image-1480.jpg" v-if="promo?.template == 'big_promo'" />
							<BaseUiImage :data="promo.image_thumbs?.['width720-height280-crop1']" alt="" default="/images/no-image-1480.jpg" v-if="promo?.template == 'medium_promo'" />
							<BaseUiImage :data="promo.image_thumbs?.['width467-height280-crop1']" alt="" default="/images/no-image-1480.jpg" v-if="promo?.template == 'small_promo'" />
						</template>
						<template v-else>
							<BaseUiImage :data="promo.image_2_thumbs?.['width276-height276']" alt="" default="/images/no-image-300.jpg" />
						</template>
					</component>
				</template>
			</div>

			<CatalogExcludeList :items="items" v-if="items?.length" />
		</div>
	</div>
</template>

<script setup>
	import { NuxtLink } from '#components';
	const {mobileBreakpoint} = inject('rwd');
	const props = defineProps(['relatedItems', 'promos', 'categoryPosition']);
	let items = props?.relatedItems ?? [];
	const categoryPosition = props?.categoryPosition;

	if(items?.length){
		items = items.filter(item => {
			const itemCategoryPosition = String(parseInt(item.category_position_h)).padStart(2, '0');
			return itemCategoryPosition === categoryPosition;
		});
	}
</script>

<style lang="less" scoped>
	.ci-promo-container{
		position: relative; background: url(assets/images/bg.jpg) center; padding: 56px 0 58px; margin-top: 78px;
		.wrapper{display: flex; flex-flow: column; gap: 58px;}
		@media (max-width: @l){
			padding: 40px 0; margin-top: 24px;
			.wrapper{gap: 40px;}
		}
		@media (max-width: @t){
			margin-top: 0; margin-top: 16px;
			.wrapper{padding: 0 30px;}
		}
		@media (max-width: @tp){
			margin-top: 0; padding: 16px;
			.wrapper{padding: 0; gap: 24px;}
		}
	}
	.ci-promo-items{
		position: relative; display: flex; flex-wrap: wrap; gap: 40px;
		@media (max-width: @l){gap: 24px;}
		@media (max-width: @tp){
			gap: 8px; flex-wrap: initial; overflow-x: auto; width: calc(~"100% - -32px"); margin-left: -16px; padding: 0 16px;
			&::-webkit-scrollbar {-webkit-appearance: none; height: 0;}
			&::-webkit-scrollbar-thumb {background-color: transparent;}
		}
	}
	.ci-promo-item{
		position: relative; display: block; line-height: 0; flex-grow: 0; flex-shrink: 0; width: 100%;
		:deep(img){display: block; width: auto; height: auto; max-width: 100%; border-radius: 2px; box-shadow: 0 10px 20px rgba(0,0,0,0.1); .transition(box-shadow);}
		&.medium_promo{width: calc(~"50% - 20px");}
		&.small_promo{width: calc(~"33.33333% - 27px");}
		@media (max-width: @l){
			&.medium_promo{width: calc(~"50% - 12px");}
			&.small_promo{width: calc(~"33.33333% - 16px");}
		}
		@media (max-width: @tp){
			width: 276px;
			&.medium_promo{width: 276px;}
			&.small_promo{width: 276px;}
		}
	}
	.ci-promo-item-link{
		@media (min-width: @h){
			&:hover :deep(img){box-shadow: 0 10px 20px rgba(0,0,0,0.3);}
		}
	}
</style>
