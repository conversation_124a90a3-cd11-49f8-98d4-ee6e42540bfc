<template>
	<ClientOnly>
		<template v-if="mode && mode == 'detail'">
			<BaseCatalogSetWishlist v-slot="{active, onToggleWishlist, loading, message}" :item="item">
				<div class="cd-wishlist" :class="{'wishlist-detail-buttons': mode == 'wishlist','active': active,}">
					<div
						class="cd-wishlist-btn"
						:class="[{
							'wishlist-detail-btn': mode == 'wishlist',
							'wishlist-detail-btn-remove': mode == 'wishlist' && active,
							'cd-wishlist-add': !active,
							'cd-wishlist-remove': active,
							'loading': loading
						}]"
						@click="onToggleWishlist">
						<BaseCmsLabel v-if="!active" code="add_to_wishlist" tag="span" />
						<BaseCmsLabel tag="span" code="remove_from_wishlist" v-if="active" />
					</div>
					<BaseCmsLabel class="product-in-wishlist cd-wishlist-message wishlist_message wishlist-message set-wishlist-message" v-show="message" :code="message" tag="span" />
				</div>
			</BaseCatalogSetWishlist>
		</template>
		<template v-else-if="mode && mode == 'cart'">
			<BaseCatalogSetWishlist v-slot="{active, onToggleWishlist, loading}" :item="item">
				<div
					class="wp-btn wp-wishlist"
					@click="onToggleWishlist"
					:class="[{
						'cp-wishlist-add': !active,
						'cp-wishlist-remove': active,
						'loading': loading
					}]">
					<BaseCmsLabel code="move_to_wishlist" tag="span" />
				</div>
			</BaseCatalogSetWishlist>
		</template>
		<template v-else>
			<BaseCatalogSetWishlist v-slot="{active, onToggleWishlist, loading, message}" :item="item">
				<div class="cp-wishlist" :class="[{'active': active || mode == 'wishlist'}, mode]">
					<div
						class="cp-wishlist-btn"
						:class="[{
							'cp-wishlist-add': !active,
							'cp-wishlist-remove': active || mode == 'wishlist',
							'loading': loading
						}]"
						@click="onToggleWishlist">
						<span>
							<BaseCmsLabel v-if="!active" code="add_to_wishlist" />
							<template v-else>
								<BaseCmsLabel v-if="mode && mode === 'wishlist'" code="remove_from_wishlist" />
								<BaseCmsLabel v-else code="remove_from_wishlist" />
							</template>
						</span>
					</div>
					<BaseCmsLabel class="product-in-wishlist cp-wishlist-message wishlist_message wishlist-message set-wishlist-message" v-show="message" :code="message" tag="span" />
				</div>
			</BaseCatalogSetWishlist>
		</template>
	</ClientOnly>
</template>

<script setup>
	const labels = useLabels();
	const props = defineProps(['item','mode']);
</script>
