<template>
	<BaseUtilsAppUrls v-slot="{items: urls}">
		<div class="header-categories-main" ref="categories">
			<BaseCatalogCategoriesWidget :fetch="{hierarhy_by_position: true}" v-slot="{items}">
				<BaseCatalogLists :fetch="{code: 'menu', response_fields: ['code', 'title', 'url_without_domain']}" v-slot="{items: list}">
					<BaseCatalogProductsWidget v-if="list?.length" :fetch="{list_code: list[0].code, limit: 3, sort: 'list_position'}" v-slot="{items: products}">
						<BaseCmsRotator :fetch="{code: 'category_promo', limit: 1, response_fields: ['id','image_upload_path','image_thumbs','image_2_upload_path','image_2_thumbs','url_without_domain']}" v-slot="{items: promos}">
							<div class="btn btn-orange header-categories" @click="toggleCategories(items[0], 1)" :class="{'active': categoriesActive}"><span><span></span><BaseCmsLabel code="catalog_categories" /></span></div>	
							<ClientOnly>
								<div class="categories-container" :class="{'active': categoriesActive, 'has-extra-content': products?.length > 0 || promos?.length, 'lvl2': mobileBreakpoint && activeCat}" :id="activeCat">
									<slot name="lang"/>
									<div class="categories-container-body">
										<ul class="nav-categories">
											<slot name="quickOrderBtn" />
											<li v-for="(item,index) in items" :key="item.id" :class="{'has-children': item?.children?.length, 'active': !mobileBreakpoint && activeCat == index+1}">
												<BaseUiLink native :to="item.url_without_domain" :prevent-default="(mobileBreakpoint && item.children?.length) ? true : false" @mouseenter="!mobileBreakpoint && setActiveCategories(item, 1, index+1)" @click="mobileBreakpoint && setActiveCategories(item, 1, index+1)"><span>{{item.title}}</span></BaseUiLink>
											</li>
											<li :class="{'active': activeCat == items.length+1}"><NuxtLink :to="urls.manufacturer" @mouseenter="!mobileBreakpoint && setActiveCategories('', '', items.length+1)"><BaseCmsLabel code="manufacturers" tag="span" /></NuxtLink></li>
											<BasePublishCategory :fetch="{code: 'instashop', response_fields: ['id', 'title', 'url_without_domain']}" v-slot="{item}">
												<li :class="{'active': activeCat == items.length+2}"><NuxtLink :to="item.url_without_domain" @mouseenter="!mobileBreakpoint && setActiveCategories('', '', items.length+2)"><span>{{item.title}}</span></NuxtLink></li>
											</BasePublishCategory>
										</ul>
										
										<div class="nav-categories-right" :class="{'active': activeCategories[0]?.children && hideLv2 == false}">
											<ul class="subcategory-list" >
												<li v-for="item in activeCategories[0]?.children" :key="item.id">
													<NuxtLink :to="item.url_without_domain"><span>{{item.title}}</span></NuxtLink>
												</li>
												<li class="subcategory-new" v-if="activeCategories[0]?.total_new > 0"><BaseUiLink :href="activeCategories[0].url_without_domain + '?new=1'"><BaseCmsLabel code="new" /></BaseUiLink></li>
												<li class="subcategory-sale" v-if="activeCategories[0]?.total_discount > 0"><BaseUiLink :href="activeCategories[0].url_without_domain + '?discount=1'"><BaseCmsLabel code="sale" /></BaseUiLink></li>
												<li class="m-all-products" v-if="activeCategories[0]?.url_without_domain"><BaseUiLink :href="activeCategories[0].url_without_domain"><BaseCmsLabel code="all_products" /></BaseUiLink></li>
											</ul>
										</div>
										
										<!-- FIXME INTEG složiti listu izdvajanja kad se složi layout proizvoda -->
										<div class="category-container-products" v-if="products?.length">
											<BaseCmsLabel code="most_sale" tag="div" class="category-container-products-title" />
											<div class="category-container-product-items">
												<template v-for="product in products" :key="product.id">
													<CatalogIndexEntry :item="product" list="Izbornik" class="cp-list" />
												</template>
											</div>
										</div>
										
										<div class="category-container-promo" v-if="promos?.length && products?.length == 0">
											<NuxtLink :to="promos[0].url_without_domain">
												<BaseUiImage :data="promos[0].image_thumbs?.['width730-height430-crop1']" :picture="[{maxWidth: '1550px', src: promos[0].image_2_thumbs?.['width430-height430-crop1'].thumb, default: '/images/no-image-715.jpg'}]" alt="" default="/images/no-image-715.jpg" />
											</NuxtLink>
										</div>
									</div>
								</div>
							</ClientOnly>
						</BaseCmsRotator>
					</BaseCatalogProductsWidget>
				</BaseCatalogLists>
			</BaseCatalogCategoriesWidget>
		</div>
	</BaseUtilsAppUrls>
</template>

<script setup>
	const route = useRoute();
	const {onClickOutside} = useDom();
	const {mobileBreakpoint} = inject('rwd');

	const categories = ref(null);
	const categoriesActive = ref(false);
	const activeCat = ref(false);
	const hideLv2 = ref(false);
	onClickOutside(categories, () => {
		categoriesActive.value = false;
	});

	// Put active/clicked categories to array
	const activeCategories = ref([]);
 	const categoryTitle = useState('categoryTitle');
	
	function toggleCategories(item,level){
		if(categoriesActive.value == false){
			categoriesActive.value = true;
			activeCat.value = 1;
			if(item.children?.length) {
				if(level == 1) {
					return activeCategories.value = [item];
				}
			}
		}else{
			categoriesActive.value = 0;
			activeCategories.value = [];
		}
	}
	function setActiveCategories(item, level, index) {
		if(item.children?.length) {
			if(level == 1) {
				categoryTitle.value = item.title;
				return activeCategories.value = [item], activeCat.value = index, hideLv2.value = false;
			}
			if(level > 1) {
				const parentCategories = activeCategories.value.slice(0, level - 1);
				return activeCategories.value = [...parentCategories, item], hideLv2.value = false;
				
			}
		}else{
			return activeCat.value = index, hideLv2.value = true;
		}
		activeCat.value = 0;
		activeCategories.value = [];
	}
	const resetState = () => {
		categoriesActive.value = true;
		activeCat.value = 0;
		activeCategories.value = [];
	};
	defineExpose({ resetState });

	watch(
		() => route.fullPath,
		(newPath, oldPath) => {
			categoriesActive.value = false;
			activeCat.value = 0;
			activeCategories.value = [];
		}
	)
</script>

<style lang="less" scoped>
	.header-categories{cursor: pointer;}
</style>
