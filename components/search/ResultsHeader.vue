<template>
	<div class="wrapper s-header-wrapper">
		<h1 class="s-header-title s-h1">
			<span class="s-headline"><BaseCmsLabel code="search_headline" /></span>
			<span class="s-keyword">{{searchTerm}}</span>
		</h1>
		<BaseThemeSearchNavigation>
			<template #title="{item}">
				<template v-if="item.id == 'publish.02'">
					<BaseCmsLabel code="search_recipes" />
				</template>
				<template v-else>
					{{item.title}}
				</template>
				<span class="s-counter">({{item.total}})</span>
			</template>
		</BaseThemeSearchNavigation>
	</div>
</template>

<script setup>
	const props = defineProps(['searchTerm']);
</script>
