<template>
	<Body class="page-publish page-author" />
	<BasePublishAuthors :use-slug="true" v-slot="{items}">
		<div class="wrapper wrapper-publish-author">
			<BasePublishPosts :fetch="{author_id: items[0]?.id}" v-slot="{items: posts, nextPage, loadMore, loading}">
				<template v-if="posts?.length">
					<div class="p-items">
						<PublishIndexEntry v-for="post in posts" :key="post.id" :item="post" />
					</div>
				</template>

				<ClientOnly>
					<div class="load-more-container" v-if="nextPage" data-posts-scroll-trigger>
						<button type="button" class="btn noarrow load-more btn-load-more" :class="{'loading': loading}" @click="loadMore"><UiLoader v-if="loading" size="small" color="white" /><BaseCmsLabel tag="span" code="load_more_publish" /></button>
					</div>
				</ClientOnly>
				<BaseUiPagination class="pagination" />
			</BasePublishPosts>
		</div>
	</BasePublishAuthors>
</template>
