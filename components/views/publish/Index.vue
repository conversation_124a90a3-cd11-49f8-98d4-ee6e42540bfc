<template>
	<Body class="page-publish-index page-publish" />
	<BasePublishCategory :root-category="true" :include-subcategories="true" v-slot="{item: category, rootCategory, }" :seo="true">
		<div class="wrapper wrapper-publish">
			<CmsBreadcrumbs v-if="category?.breadcrumbs" :items="category.breadcrumbs" />
			<BasePublishPosts :featured-posts="1" v-slot="{items: posts, nextPage, loadMore, featuredPosts, loading}">
				<div class="pos-r p-intro">
					<template v-if="category.level == 1">
						<BaseCmsLabel code="homepage_health" tag="div" class="pw-title" />
					</template>
					<template v-else>
						<div v-if="category?.seo_h1" class="pw-title p-title" v-html="category.seo_h1" v-interpolation></div>
					</template>
					<PublishSubcategories :category="category" :rootCategory="rootCategory" />

					<div class="p-intro-items">
						<BaseSearchResults :search-modules="[{module: 'catalog'}, {module: 'publish|01'}, {module: 'publish|02'}, {module: 'cms'}]" v-slot="{searchContent}"> {{searchContent}}fdfdf </BaseSearchResults>
						<template v-if="featuredPosts?.length">
							<div :class="{'p-intro-col p-intro-col1': category?.level == 1}">
								<PublishIndexEntry :mode="category?.level < 2 ? 'big' : 'big2'" v-for="featuredPost in featuredPosts" :key="featuredPost.id" :item="featuredPost" />
							</div>
						</template>

						<BasePublishPostsWidget v-if="category?.level == 1" :fetch="{category_code: category.code, extra_fields: ['short_description'], sort: 'most_view', limit: 4}" v-slot="{items}">
							<div class="p-intro-col p-intro-col2" v-if="items?.length">
								<BaseCmsLabel code="special_articles_title" class="p-intro-title" tag="div" />
								<PublishIndexEntry mode="small" v-for="item in items" :key="item.id" :item="item" />
							</div>
						</BasePublishPostsWidget>
					</div>
				</div>

				<div class="p-index-items" v-if="posts?.length">
					<div class="p-items">
						<PublishIndexEntry v-for="post in posts" :key="post.id" :item="post" />
					</div>
				</div>
				<div v-else class="p-empty"><BaseCmsLabel code="no_publish" /></div>

				<ClientOnly>
					<div class="load-more-container" v-if="nextPage" data-posts-scroll-trigger>
						<button type="button" class="btn noarrow load-more btn-load-more" :class="{'loading': loading}" @click="loadMore"><UiLoader v-if="loading" size="small" color="white" /><BaseCmsLabel tag="span" code="load_more_publish" /></button>
					</div>
				</ClientOnly>
				<BaseUiPagination class="pagination" />
			</BasePublishPosts>
		</div>
	</BasePublishCategory>
</template>
