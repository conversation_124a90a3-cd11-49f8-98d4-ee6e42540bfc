<template>
	<Body class="page-search white-bg" />
	<BaseCmsPage>
		<div class="search-body">
			<BaseSearchResults :search-modules="[{module: 'catalog'}, {module: 'publish|01'}, {module: 'publish|02'}, {module: 'cms'}]" v-slot="{items, searchContent, searchTerm}">
				<div class="search-container">
					<template v-if="searchContent == 'publish.01'">
						<div class="wrapper wrapper-publish">
							<BasePublishPosts v-slot="{items, nextPage, loading, loadMore}">
								<div class="p-index-items">
									<div class="p-items" v-if="items">
										<template v-for="post in items" :key="post.id">
											<PublishIndexEntry :item="post" :short-description="true" />
										</template>
									</div>
									<ClientOnly>
										<div class="load-more-container" v-if="nextPage" data-posts-scroll-trigger>
											<button type="button" class="btn noarrow load-more btn-load-more" :class="{'loading': loading}" @click="loadMore"><UiLoader v-if="loading" size="small" color="white" /><BaseCmsLabel code="load_more_publish" tag="span" /></button>
										</div>
										<BaseUiPagination class="pagination" />
									</ClientOnly>
								</div>
							</BasePublishPosts>
						</div>
					</template>
					<template v-if="searchContent == 'publish.02'">
						<div class="wrapper wrapper-publish">
							<BasePublishPosts v-slot="{items, nextPage, loading, loadMore}">
								<div class="p-index-items">
									<div class="p-items" v-if="items">
										<template v-for="post in items" :key="post.id">
											<PublishIndexEntry :item="post" :short-description="true" />
										</template>
									</div>
									<ClientOnly>
										<div class="load-more-container" v-if="nextPage" data-posts-scroll-trigger>
											<button type="button" class="btn noarrow load-more btn-load-more" :class="{'loading': loading}" @click="loadMore"><UiLoader v-if="loading" size="small" color="white" /><BaseCmsLabel code="load_more_publish" tag="span" /></button>
										</div>
										<BaseUiPagination class="pagination" />
									</ClientOnly>
								</div>
							</BasePublishPosts>
						</div>
					</template>
					<template v-if="searchContent == 'cms' && items?.cms">
						<div class="s-cms-wrapper s-wrapper">
							<div class="search-cnt">
								<BaseCmsPage :fetch="{mode: 'search', 'search_q': searchTerm}" v-slot="{page}" :seo="false">
									<article class="s-item" v-for="item in page.items" :key="item.id">
										<h2 class="s-item-title">
											<NuxtLink :to="item.url_without_domain">{{ item.title }}</NuxtLink>
										</h2>
										<div class="s-item-content" v-if="item?.content" v-html="limitWords(stripHtml(item.content), 50)" />
									</article>
								</BaseCmsPage>
							</div>
						</div>
					</template>
				</div>
			</BaseSearchResults>
		</div>
	</BaseCmsPage>
</template>

<script setup>
	const {stripHtml, limitWords} = useText();
</script>
