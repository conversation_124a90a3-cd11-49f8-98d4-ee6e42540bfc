<template>
	<Body class="page-catalog page-catalog white-bg" :class="{'active-filter': activeFilters, 'toolbar-stuck': isStuck}" />
	<BaseCatalogCategory v-slot="{item: category, contentType}" :seo="true">
		<BaseCatalogProducts v-slot="{items: products, loading, pagination, nextPage, loadMore}">
			<BaseCatalogFilters v-slot="{searchFields, selectedFiltersCounter}">
				<BaseCatalogActiveFilters v-slot="{items: activeFilter}">
					<ClientOnly>
						<template v-if="contentType != 'search'">
							<template v-if="category?.level">
								<div class="c-header">
									<div class="wrapper">
										<div class="c-header-main" :class="{'has-category-image': category?.main_image?.length && contentType == 'category' && !mobileBreakpoint}">
											<div class="c-category-image" v-if="category?.main_image?.length && contentType == 'category' && !mobileBreakpoint">
												<BaseUiImage :src="category.main_image" />
											</div>
											<CmsBreadcrumbs v-if="category?.breadcrumbs" :items="category.breadcrumbs" class="bc bc-catalog ci-bc" />
											<div class="c-header-col">
												<h1 class="c-title" :class="{['c-title-level' + category.level]: true}" v-if="category?.title">{{ category.title }}</h1>

												<div class="c-desc" :class="{'has-border': contentType == 'manufacturer'}" v-if="category?.content?.length && !mobileBreakpoint">
													<div class="c-desc-cnt lists" v-html="category.content"></div>
												</div>

												<BaseUiToggleContent
													class="c-desc"
													v-if="category?.content?.length && mobileBreakpoint"
													:class="{'has-border': contentType == 'manufacturer'}"
													:content="category.content"
													:limit="120"
													:append="{
														show: {
															title: labels.get('show_all_description')
														}
													}"
													v-slot="{content}">
													<div class="c-desc-cnt lists" v-html="content" />
												</BaseUiToggleContent>

												<BaseCatalogCategoriesWidget :fetch="{start_position: category.position_h, hierarhy_by_position: true}" v-slot="{items: categories}">
													<div class="ci-categories" v-if="categories?.length">
														<NuxtLink class="ci-category" v-for="category in categories" :key="category.id" :to="category.url_without_domain" :class="{'no-image': !category?.main_image}">
															<BaseUiImage v-if="category?.main_image" :src="category.main_image" loading="lazy" width="36" height="36" :alt="category.title" default="/images/no-image-50.jpg" />
															<span>{{category.title}}</span>
														</NuxtLink>
														<NuxtLink class="ci-category new" :to="category.url_without_domain + '?new=1'"><BaseCmsLabel code="new" tag="span" /></NuxtLink>
														<NuxtLink class="ci-category sale" :to="category.url_without_domain + '?discount=1'"><BaseCmsLabel code="sale" tag="span" /></NuxtLink>
													</div>
												</BaseCatalogCategoriesWidget>
											</div>
										</div>
										<div class="c-header-image" v-if="category?.main_image_2?.length && contentType == 'category' && !mobileBreakpoint">
											<BaseUiImage :data="category.main_image_2_thumbs?.['width505-height505']" alt="" default="/images/no-image-715.jpg" />
										</div>
									</div>
								</div>
							</template>

							<BaseCatalogProductsWidget :fetch="{related_code: 'bestsellers', limit: 5}" v-slot="{items: relatedItems}">
								<BaseCmsRotator :fetch="{code: 'catalog_category_promo', catalogcategory_id: category.id, response_fields: ['id', 'link','image_upload_path','image_thumbs', 'image_2_upload_path', 'image_2_thumbs', 'url_without_domain', 'template']}" v-slot="{items: promos}">
									<CatalogCategoryPromos :relatedItems="relatedItems" :promos="promos" :categoryPosition="category.position_h" />
								</BaseCmsRotator>
							</BaseCatalogProductsWidget>
						</template>

						<div class="ci-main" :class="{'manufacturer': contentType == 'manufacturer'}">
							<div class="c-toolbar" v-if="products?.length" :class="{'stuck': isStuck}" ref="cToolbar">
								<div class="wrapper">
									<div class="btn-toggle-filter btn-toggle-catalog-filter" @click="activeFilters = !activeFilters">
										<span><span class="filter-icon"></span><BaseCmsLabel code="filters" /></span>
									</div>
									<div class="filters-container" :class="{'active': activeFilters}">
										<div class="filters-container-header" @click="activeFilters = !activeFilters" v-if="mobileBreakpoint">
											<span><span class="filter-icon"></span><BaseCmsLabel code="filters" /></span>
											<div class="cf-filter-close-icon"></div>
										</div>
										<CatalogFilters @close-filters="activeFilters = false;" :total-products="pagination.items.total" :search-fields="searchFields" :content-type="contentType" :selectedFilters="selectedFiltersCounter" />
										<div class="cf-special">
											<BaseCatalogSpecialFilter filter="with_qty" v-if="products?.length || route.query?.with_qty" />
											<BaseCatalogSpecialFilter filter="discount" v-if="products?.length || route.query?.discount" />
										</div>
									</div>
									<CatalogSort />
								</div>
							</div>

							<div id="items_catalog_layout" :class="{'loading': loading}" class="ci-layout wrapper">
								<template v-if="products?.length">
									<div class="c-items c-items-main" :class="{'c-items3': tabletSmallBreakpoint, 'c-items5': !tabletSmallBreakpoint}">
										<template v-for="product in products" :key="product.id">
											<CatalogIndexEntry :item="product" list="" />
										</template>
									</div>
									<div class="c-items-footer" v-if="loading || nextPage">
										<div class="load-more-container c-load-more-container" v-if="nextPage" data-products-scroll-trigger>
											<button type="button" class="btn load-more btn-load-more btn-load-more-catalog" :class="{'loading': loading}" @click="loadMore()"><UiLoader v-if="loading" /><BaseCmsLabel code="load_more_catalog" tag="span" /></button>
										</div>
										<BaseUiPagination class="pagination c-pagination-items" v-if="nextPage" />
									</div>
								</template>
								<div v-else class="c-empty"><BaseCmsLabel code="no_products" /></div>
							</div>
						</div>

						<!-- FIXME INTEG provjeriti bestsellers listu izdvajanja -->
					</ClientOnly>
				</BaseCatalogActiveFilters>
			</BaseCatalogFilters>
		</BaseCatalogProducts>
	</BaseCatalogCategory>
</template>

<script setup>
	const activeFilters = ref(false);
	const route = useRoute();
	const {prependTo, appendTo, insertAfter, onMediaQuery} = useDom();
	const {tabletSmallBreakpoint} = inject('rwd');
	const emit = defineEmits(['closeFilters']);
	const labels = useLabels();

	const cToolbar = ref(null);
	const isStuck = ref(false);
	const checkSticky = () => {
		if (cToolbar.value) {
			isStuck.value = cToolbar.value.getBoundingClientRect().top <= 70;
		}
	};

	onMounted(() => {
		window.addEventListener('scroll', checkSticky);
		checkSticky();
	});

	onUnmounted(() => {
		window.removeEventListener('scroll', checkSticky);
	});

	const {matches: mobileBreakpoint} = onMediaQuery({
		query: '(max-width: 990px)',
		timeout: 600,
		enter: () => {
			prependTo('.filters-container', '.page-wrapper');
			insertAfter('.cf-special', '.c-toolbar');
		},
		leave: () => {
			window.location.reload();
		},
	});
</script>

<style lang="less" scoped>
	.c-header{
		.wrapper{display: flex; padding-left: 0; position: relative;}
	}
	.c-header-main{
		padding: 40px 0 0; flex-grow: 1; flex-shrink: 1; position: relative;
		&.has-category-image{padding-left: 83px; margin-left: 42px;}
		@media (max-width: @tp){padding: 16px; width: 100%;}
	}
	.c-category-image{position: absolute; left: 0px; top: 45px; width: 64px; height: 64px;}
	.c-header-image{
		flex-grow: 0; flex-shrink: 0; margin-left: 30px; line-height: 0;
		img{display: block; width: auto; height: auto; max-width: 100%;}
		@media (max-width: @t){max-width: 40%;}
	}
	.ci-categories{
		position: relative; display: flex; flex-wrap: wrap; gap: 8px; margin-top: 16px;
		@media (max-width: @tp){
			flex-wrap: initial; width: calc(~"100% - -32px"); margin-left: -16px; padding: 0 16px; overflow-x: auto;
			&::-webkit-scrollbar {-webkit-appearance: none; height: 0;}
			&::-webkit-scrollbar-thumb {background-color: transparent;}
		}
	}
	.ci-category{
		position: relative; display: flex; align-items: center; gap: 8px; font-size: 14px; line-height: 16px; padding: 5px 16px 5px 6px; border: 1px solid #DEDEDE; border-radius: 2px; text-decoration: none; min-height: 48px; .transition(border-color,color);
		&.no-image{padding: 14px 16px;}
		&.new, &.sale{
			font-weight: 700;
			span{
				display: flex; align-items: center;
				&:before{content: ""; position: relative; display: flex; align-items: center; justify-content: center; width: 36px; height: 36px; background: url(/assets/images/new.svg) center no-repeat; margin-right: 8px;}
			}
		}
		&.sale{
			background: @red; color: #fff; border-color: @red; transition: background 0.3s, border-color 0.3s;
			span:before{background: url(/assets/images/sale.svg) center no-repeat;}
		}
		@media (min-width: @h){
			&:hover{border-color: @lightGreen;}
			&.sale:hover{border-color: darken(@red,10%); background: darken(@red,10%);}
		}
		@media (max-width: @tp){flex-grow: 0; flex-shrink: 0;}
	}

	.ci-main{
		position: relative; padding-top: 0; margin-bottom: 54px;
		&.manufacturer{padding-top: 0;}
		@media (max-width: @tp){z-index: 51;}
	}
	.c-toolbar{
		position: sticky; top: 70px; z-index: 49;  padding-top: 24px;
		&.stuck{box-shadow: 0 15px 40px rgba(0,0,0,0.2); z-index: 49; background: #fff;}
		.wrapper{display: flex; align-items: flex-start; justify-content: space-between;}
		@media (max-width: @t){
			.wrapper{width: 100%; padding: 0 30px;}
		}
		@media (max-width: @tp){
			padding: 16px 0; top: 0;
			.wrapper{padding: 0 16px; flex-wrap: wrap;}
		}
	}
	.filters-container{
		position: relative; display: flex; align-items: flex-start; background: #fff;
		@media (max-width: @tp){
			position: fixed; top: 0; right: 0; bottom: 0; left: 0; flex-flow: column; z-index: 1000; padding-top: 52px; max-height: 100svh; overflow-y: auto; display: none;
			&.active{display: flex;}
		}
	}
	.filters-container-header{
		position: fixed; top: 0; left: 0; right: 0; background: url(assets/images/header-t.jpg) center no-repeat; height: 52px; display: flex; align-items: center; justify-content: space-between; color: #fff; padding: 0 16px; order: 1; z-index: 1000;
		&>span{position: relative; display: flex; align-items: center;}
	}
	.cf-special{
		display: flex; align-content: center; margin-left: 32px; gap: 24px; height: 50px;
		@media (max-width: @l){margin-left: 12px; gap: 12px;}
		@media (max-width: @tp){order: 2; margin: 0; padding: 0 22px 16px; width: 100%; justify-content: space-between; flex-grow: 0; flex-shrink: 0; height: auto;}
	}
	:deep(.cf-with_qty), :deep(.cf-discount){
		position: relative; display: flex; align-items: center;
		input[type=checkbox]+label{
			padding-left: 0; padding-right: 40px; position: relative; padding-top: 0; display: flex; align-items: center; font-weight: 700; font-size: 14px; line-height: 16px; white-space: nowrap;
			&:before{.pseudo(30px,10px); border-radius: 5px; background: #898989; right: 0; left: auto; border: none; top: auto; margin-top: 0;}
			&:after{.pseudo(20px,20px); background: #fff; border-radius: 50%; right: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.25); .transition(right);}
		}
		input[type=checkbox]:checked+label{
			&:before{background-image: linear-gradient(180deg, rgba(171, 192, 117, 1) 0%, rgba(128, 153, 65, 1) 100%);}
			&:after{right: 0;}
		}
	}
	:deep(.cf-discount){
		input[type=checkbox]+label{color: @red;}
		input[type=checkbox]:checked+label:before{background: @red;}
	}

	.ci-layout{
		@media (max-width: @t){padding: 0 30px;}
		@media (max-width: @tp){padding: 0 16px;}
	}
</style>
