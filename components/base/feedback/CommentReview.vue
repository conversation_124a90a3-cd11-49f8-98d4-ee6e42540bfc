<template>
	<slot :status="msg" :rateUp="rateUp" :rateDown="rateDown" :positiveReviews="positiveReviews" :negativeReviews="negativeReviews" />
</template>

<script setup>
	const props = defineProps({
		comment: {
			required: true,
		},
	});
	const {rateComment} = useFeedback();

	const msg = ref(null);
	const positiveReviews = ref(props.comment.review_positive);
	const negativeReviews = ref(props.comment.review_negative);

	// clear message after 3 seconds
	watch(
		() => msg.value,
		() => {
			if (msg.value) {
				setTimeout(() => {
					msg.value = null;
				}, 3000);
			}
		}
	);

	// positive rate
	async function rateUp() {
		await rateComment(props.comment.id, 1).then(res => {
			msg.value = res.data?.label_name ? res.data.label_name : '';
			if (res.success) positiveReviews.value++;
		});
	}

	// negative rate
	async function rateDown() {
		await rateComment(props.comment.id, -1).then(res => {
			msg.value = res.data?.label_name ? res.data.label_name : '';
			if (res.success) negativeReviews.value++;
		});
	}
</script>
