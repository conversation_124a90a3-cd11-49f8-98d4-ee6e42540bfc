<template>
	<slot :rate="Number(data?.rates).toFixed(1)" :stars="stars" :counter="data?.rates_votes" v-if="data?.rates_status > 1" />
</template>

<script setup>
	const props = defineProps(['data']);

	let stars = '';
	for (let i = 1; i <= 5; i++) {
		const active = i <= Math.round(props.data?.rates) ? ' icon-star active' : '';
		stars += '<span class="icon-star-empty' + active + '"></span>';
	}
</script>
