<template>
	<slot :onToggleWishlist="onToggleWishlist" :active="active" :message="message" :loading="loading" :wishlistUrl="wishlistUrl">
		<span class="base-setwishlist">
			<span class="base-setwishlist-btn" :class="{'loading': loading, 'active': active}" @click="onToggleWishlist">{{ active ? '-' : '+' }}</span>
			<span class="base-setwishlist-message" v-show="message" v-html="labels.get(message).replace('%view_wishlist_url%', wishlistUrl)" />
		</span>
	</slot>
</template>

<script setup>
	const {getAppUrl} = useApiRoutes();
	const labels = useLabels();
	const catalog = useCatalog();
	let fbCapi;
	if (__FB_CAPI_TRACKING__) fbCapi = useFbCapi();

	let gtm;
	if (__GTM_TRACKING__) gtm = useGtm();

	const props = defineProps({
		item: {
			type: Object,
			required: true,
		},
	});

	// set initial active state
	const wishlistProducts = computed(() => catalog.getWishlistProducts());
	const active = computed(() => {
		return wishlistProducts?.value?.items?.find(el => el.shopping_cart_code == props.item?.shopping_cart_code) ? true : false;
	});

	const message = ref('');
	const loading = ref(false);
	const wishlistUrl = getAppUrl('wishlist');

	let messageTimeout;
	async function onToggleWishlist(options) {
		if (loading.value) return; // prevent multiple clicks

		clearTimeout(messageTimeout);
		loading.value = true;
		message.value = '';

		let res;
		if (active.value || options?.remove) {
			res = await catalog.addRemoveWishlistProducts({shopping_cart_code: props.item.shopping_cart_code, qty: 0});
		} else {
			res = await catalog.addRemoveWishlistProducts({shopping_cart_code: props.item.shopping_cart_code, qty: 1});
			if (gtm && res.success) gtm.gtmTrack('addToWishlist', {items: props.item});

			if (fbCapi && res.success) {
				fbCapi.sendEvent('addToWishlist', {
					content_ids: [props.item.code],
					content_category: props.item.category_title,
					content_type: 'product',
					content_name: props.item.title,
					value: props.item.price_custom || 0,
				});
			}
		}

		await catalog.fetchWishlistProducts({retry: active.value ? 'remove' : 'add', shopping_cart_code: props.item.shopping_cart_code});
		loading.value = false;
		message.value = res?.data?.label_name || '';
		messageTimeout = setTimeout(() => (message.value = ''), 4000);
	}
</script>
