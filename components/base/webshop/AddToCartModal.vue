<template>
	<Body v-if="items.length" :class="['modal-active', 'modal-add-to-cart-active']" />
	<slot :items="items" :products="products" :status="status" :onClose="onClose" :urls="urls" />
</template>

<script setup>
	const eventBus = useEventBus();
	const emit = defineEmits(['load', 'close']);
	const {getAppUrls} = useApiRoutes();
	const catalog = useCatalog();
	const {generateThumbs} = useImages();
	const props = defineProps({
		autoClose: {
			type: Number,
			default: 4000,
		},
		thumbPreset: String,
	});

	// products that are added to cart
	const items = shallowRef([]);
	const products = shallowRef([]);
	const status = ref(null);

	// provide global urls to modal
	const urls = getAppUrls();

	// listen when product is added to cart
	let modalTimeout;

	eventBus.subscribe(
		'webshopAddedToCart',
		async data => {
			// reset timeout if new product is added and modal is already open
			if (modalTimeout) clearTimeout(modalTimeout);

			// set response status of added products
			if (data.status) status.value = data.status;
			if (data.products) products.value = data.products;

			// set added products
			items.value = data.products;

			// set modal products data from emitted event
			let p = [];
			data.products?.forEach(product => {
				if (product.modalData) p.push(product.modalData);
			});
			if (p.length) items.value = p;

			emit('load', data.products ? data.products : null);

			// generate thumbs
			if (items.value?.length && props.thumbPreset) {
				let itemsCopy = [...items.value];
				await generateThumbs({
					data: itemsCopy,
					preset: props.thumbPreset,
				});
				items.value = itemsCopy;
			}

			// auto close modal after x seconds
			if (props.autoClose === 0) return;
			modalTimeout = setTimeout(() => {
				onClose();
			}, props.autoClose);
		},
		{deep: false}
	);

	// close modal and reset items
	function onClose() {
		items.value = [];
		status.value = null;
		products.value = [];
		eventBus.emit('webshopAddToCartClose');
		emit('close');
	}

	onBeforeUnmount(() => {
		clearTimeout(modalTimeout);
	});
</script>
