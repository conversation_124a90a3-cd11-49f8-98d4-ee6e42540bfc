<template>
	<slot :cart="cartData" :user="user" :customer="customer" :giftCards="giftCards" :urls="getAppUrls()" />
</template>

<script setup>
	const auth = useAuth();
	const webshop = useWebshop();
	const {getAppUrl, getAppUrls} = useApiRoutes();
	const cartData = computed(() => {
		return webshop.getCartData();
	});
	const user = computed(() => auth.getUser());
	const customer = useState('customer');
	const props = defineProps({
		log: {
			type: Boolean,
			default: false,
		},
	});

	let remarketing;
	if (__REMARKETING__) remarketing = useRemarketing();

	// filter customer gift cards only for items that are in the cart. Also match the quantity of the items in the cart
	const giftCards = computed(() => {
		if (!customer.value?.couponrecipient) return [];
		if (!cartData.value?.parcels[0]?.items.length) return [];
		const cartItems = cartData.value?.parcels[0]?.items;

		const cartItemCodesQuantities = cartItems.reduce((acc, item) => {
			acc[item.shopping_cart_code] = (acc[item.shopping_cart_code] || 0) + item.quantity;
			return acc;
		}, {});

		const filteredData = Object.entries(customer.value.couponrecipient).reduce((acc, [key, value]) => {
			if (cartItemCodesQuantities[key]) {
				if (value.length > cartItemCodesQuantities[key]) {
					acc[key] = value.slice(0, cartItemCodesQuantities[key]);
				} else {
					acc[key] = value;
				}
			}
			return acc;
		}, {});

		return Object.entries(filteredData).length ? Object.entries(filteredData) : [];
	});

	onMounted(async () => {
		// fetch customer data when checkout is initialized
		await webshop.fetchCustomer();

		// redirect to cart page if there are errors
		if (cartData.value?.cart?.errors?.cart?.length) {
			useLog('Cart has errors. Redirecting to cart page');
			setTimeout(async () => {
				return navigateTo(getAppUrl('webshop_shopping_cart'));
			}, 1000);
		}

		// redirect to cart page if there are warnings
		if (cartData.value?.cart?.warnings?.cart?.length) {
			useLog('Cart has warnings. Redirecting to cart page');
			setTimeout(async () => {
				return navigateTo(getAppUrl('webshop_shopping_cart'));
			}, 1000);
		}

		if (remarketing) {
			const route = useRoute();
			let t;
			if (t) clearTimeout(t);
			t = setTimeout(() => {
				if (!route?.query?.order_identificator) remarketing.sendEvent('conversionintent', {items: cartData.value?.parcels[0]?.items || [], total: cartData.value?.total?.total_items_total || 0});
			}, 500);
		}

		if (props.log) {
			setTimeout(() => {
				console.log('BaseWebshopCheckout', {cart: cartData.value, customer: customer.value, giftCards: giftCards.value});
			}, 1000);
		}
	});
</script>
