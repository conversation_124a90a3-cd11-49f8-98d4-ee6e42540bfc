<template>
	<slot :onSubmit="onSubmit" :onRemove="onRemove" :couponCode="couponCode" :message="message" :error="error" :loading="loading" :activeCoupon="activeCoupon" :handleInput="handleInput">
		<div class="base-coupon" :class="[mode, {'base-coupon-error': error}, {'base-coupon-active': activeCoupon}, {'loading': loading}]">
			<form v-if="mode == 'webshop'" class="base-coupons-form" @submit.prevent="couponCode && !activeCoupon && onSubmit()">
				<input class="base-coupons-input" :readonly="activeCoupon?.code" :placeholder="activeCoupon?.code ? activeCoupon.code : labels.get('coupon_code')" type="text" name="coupon" v-model="couponCode" />
				<button v-if="!activeCoupon" class="base-coupons-btn base-coupons-btn-add" type="submit" :disabled="!couponCode"><BaseCmsLabel code="coupon_add" /></button>
				<button v-if="activeCoupon" class="base-coupons-btn base-coupons-btn-remove" type="button" @click="onRemove"><BaseCmsLabel code="coupon_remove" /></button>
			</form>
			<form v-else class="base-coupons-form" @submit.prevent="onSubmit()">
				<input class="base-coupons-input" :placeholder="labels.get('coupon_code')" type="text" name="coupon" v-model="couponCode" />
				<button class="base-coupons-btn base-coupons-btn-add" type="submit" :disabled="!couponCode"><BaseCmsLabel code="coupon_add" /></button>
			</form>
			<BaseCmsLabel v-if="message" class="base-coupons-status" tag="div" :code="message" />
		</div>
	</slot>
</template>

<script setup>
	const labels = useLabels();
	const webshop = useWebshop();
	const auth = useAuth();
	const {emit} = useEventBus();
	const props = defineProps({
		mode: {
			type: String,
			default: 'webshop',
		},
	});
	const couponCode = ref('');
	const message = ref('');
	const error = ref(0);
	const loading = ref(false);
	const cartData = computed(() => webshop.getCartData());
	const activeCoupon = ref(null);

	watchEffect(() => {
		if (cartData.value?.total?.extraitems) {
			activeCoupon.value = cartData.value.total.extraitems.find(el => el.type == 'coupon' && el.code);
		}
	});

	// clear status message after 4 seconds
	let couponTimeout;
	watch(
		() => message.value,
		async () => {
			if (!message.value) return;
			couponTimeout = setTimeout(async () => {
				message.value = '';
			}, 4000);
		}
	);

	function handleInput(event) {
		couponCode.value = event.target.value;
	}

	//UAU20
	async function onSubmit() {
		clearTimeout(couponTimeout);
		loading.value = true;
		message.value = '';
		const res = props.mode == 'auth' ? await auth.addCoupon({code: couponCode.value}) : await webshop.addCoupon({code: couponCode.value});

		// if coupon is added successfully, emit event and clear input. Only in "auth" mode
		if (res.success && props.mode == 'auth') {
			emit('couponAdded', res);
			couponCode.value = '';
		}

		error.value = !res.success ? 1 : 0;
		message.value = res.data.label_name;
		loading.value = false;
	}

	async function onRemove() {
		clearTimeout(couponTimeout);
		loading.value = true;
		message.value = '';
		error.value = 0;
		couponCode.value = '';
		await webshop.removeCoupon({code: activeCoupon.value.code});
		loading.value = false;
	}

	onBeforeUnmount(() => {
		clearTimeout(couponTimeout);
	});
</script>
