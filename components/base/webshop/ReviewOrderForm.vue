<template>
	<BaseForm @submit="onSubmit" :loading="loading" v-slot="{errors, meta, values}">
		<slot :errors="errors" :meta="meta" :values="values" :fields="fields" :customer="customerData" :total="total" :shipping="shipping" :shippingLocation="shippingLocation" :payment="payment" :loading="loading" :orderErrors="orderErrors" :submitted="submitted" />
	</BaseForm>
</template>

<script setup>
	const emit = defineEmits(['load', 'submit']);
	const config = useAppConfig();

	let gtm;
	if (__GTM_TRACKING__) gtm = useGtm();

	let recaptcha;
	if (__RECAPTCHA__) recaptcha = useRecaptcha();

	const auth = useAuth();
	const webshop = useWebshop();
	const locations = useLocations();
	const {getAppUrl} = useApiRoutes();
	const {getCurrency} = useCurrency();
	let fbCapi;
	if (__FB_CAPI_TRACKING__) fbCapi = useFbCapi();
	const cartData = computed(() => webshop.getCartData());
	const loading = ref(false);
	const fields = ref(null);
	const submitted = ref(false);

	// emit event when form is loaded. Can be used to trigger analytics event or similar
	onMounted(async () => {
		emit('load');
		fields.value = await auth.fetchForm({type: 'webshop.review-order'}).then(res => res.data);
	});

	const customerData = computed(() => cartData.value?.customer);
	const total = computed(() => cartData.value?.total);

	// selected shipping data
	const shipping = computed(() => {
		const parcels = cartData.value?.parcels;
		let shippingData = null;

		if (!parcels?.length) return null;

		// get selected shipping method from the first parcel
		if (parcels[0]?.shipping?.selected) {
			shippingData = parcels[0].shipping.selected;
		}

		// get selected pickup location from the first parcel
		if (parcels[0]?.shipping?.pickup_location?.selected) {
			shippingData.pickup_location = parcels[0].shipping.pickup_location.selected;
		}

		return shippingData;
	});

	// get data for selected pickup location
	const shippingLocation = ref(null);
	watchEffect(async () => {
		if (shipping.value?.pickup_location?.id) {
			const pointRes = await locations.fetch({
				id: shipping.value.pickup_location.id,
			});
			shippingLocation.value = pointRes?.data?.length ? pointRes.data[0] : null;
		}
	});

	// selected payment data
	const payment = computed(() => {
		return cartData.value?.cart?.payments?.selected?.length ? cartData.value.cart.payments.selected[0] : null;
	});

	// check if customer entered all required data
	const orderErrors = computed(() => {
		if (submitted.value) return null;
		let errors = {};

		if (
			!cartData.value?.customer?.first_name ||
			!cartData.value?.customer?.last_name ||
			!cartData.value?.customer?.email ||
			!cartData.value?.customer?.phone ||
			!cartData.value?.customer?.address?.street ||
			!cartData.value?.customer?.address?.zipcode ||
			!cartData.value?.customer?.address?.city
		) {
			errors.customer = 'error_missing_customer';
		}
		if (!shipping.value || (shipping.value?.widget == 'location' && !shipping.value.pickup_location?.id) || (shipping.value?.parcel_locker && !shipping.value?.parcel_locker_info?.parcel_locker_id)) {
			errors.shipping = 'error_missing_shipping';
		}
		if (!payment.value) {
			errors.payment = 'error_missing_payment';
		}
		if (cartData.value?.cart?.errors?.cart?.length) {
			errors.cart = 'error_cart_error';
		}

		return Object.keys(errors).length === 0 ? null : errors;
	});

	// finish shopping
	async function onSubmit({values}) {
		submitted.value = true;
		emit('submit', values);
		loading.value = true;
		let orderData = null;

		// If recaptcha is enabled, fetch the token and add it to the data
		if (recaptcha) {
			const recaptchaToken = await recaptcha.fetchRecaptchaToken({action: 'webshop-review-order'});
			if (recaptchaToken) values['g-recaptcha-response'] = recaptchaToken;
		}

		// submit customer data (confirmed terms)
		const customerData = await webshop.submitCustomerData(values, {type: 'webshop.review-order', fetchCart: false});
		if (!customerData.success) {
			alert(customerData?.data?.label_name);
			return window.location.reload();
		}

		//return navigateTo(getAppUrl('webshop_failed_order'));
		const cartRes = await webshop.fetchCart({fullResponse: true});
		const order = await webshop.createOrder(cartRes);

		// get order id and code
		const orderId = order?.data?.id_code || null;

		// gtm tracking
		if (orderId && gtm) {
			gtm.gtmTrack('purchase', {
				cart: cartData.value,
				transaction_id: orderId,
			});
		}

		if (fbCapi && orderId) {
			fbCapi.sendEvent('purchase', {
				content_ids: cartRes.data?.parcels[0]?.items.map(el => el.code),
				contents: cartRes.data?.parcels[0]?.items.map(el => {
					return {
						id: el.code,
						quantity: el.quantity,
						item_price: el.total,
					};
				}),
				currency: getCurrency()?.code,
				value: cartRes.data?.total?.total,
				order_id: orderId,
				content_type: 'product',
				num_items: cartRes.data?.total?.items,
			});
		}

		await webshop.handleOrder({
			order,
			orderId,
			failedCallback: () => (loading.value = false),
		});

		loading.value = false;
	}
</script>
