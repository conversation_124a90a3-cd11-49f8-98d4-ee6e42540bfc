<template>
	<slot :onRemove="remove" :loading="loading">
		<span class="cart-btn-remove" :class="{'loading': loading}" @click="remove">
			<BaseCmsLabel code="remove" />
		</span>
	</slot>
</template>

<script setup>
	const webshop = useWebshop();
	const config = useAppConfig();

	let gtm;
	if (__GTM_TRACKING__) gtm = useGtm();

	const props = defineProps({
		item: {
			type: Object,
			required: true,
		},
	});

	const loading = ref(false);
	const {events} = inject('baseWebshopCartData', {events: null});

	async function remove(payload) {
		loading.value = true;
		const res = await webshop.removeProduct([{shopping_cart_code: props.item.shopping_cart_code}]);
		if (res.removeResponse?.success && gtm) {
			gtm.gtmTrack('removeProduct', {items: props.item});
		}
		events.value = {event: 'removeProduct', product: props.item, ...res};
		loading.value = false;
	}
</script>
