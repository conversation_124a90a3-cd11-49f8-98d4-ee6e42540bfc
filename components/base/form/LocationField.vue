<template>
	<input type="text" :class="[{'loading': loading}, {'field_error_input': errorMessage}, fieldCssClass]" :id="id" :name="name" autocomplete="off" v-model="inputValue" @keydown="handleInput" @focus="onFocus" @blur="onBlur" v-bind="$attrs" />
	<span class="autocomplete-container location-autocomplete-container" :class="{'loading': loading}" v-show="locations?.length">
		<ul class="ui-autocomplete locations-ui-autocomplete">
			<li :class="['ui-menu-item', {'active': index == selectedIndex}]" v-for="(location, index) in locations" :data-location="index" :key="location.id" @mousedown="selectLocation(location)">{{ location.zipcode }} {{ location.title }}</li>
		</ul>
	</span>
</template>

<script setup>
	import {useField, defineRule, useFieldValue, useValidateField, useIsSubmitting} from 'vee-validate';
	const emit = defineEmits(['input']);
	const webshop = useWebshop();
	const labels = useLabels();
	const props = defineProps({
		id: String,
		name: String,
		value: [String, Number],
		field: Object,
		class: String,
	});

	const loading = ref(false);
	const locations = ref([]);
	const totalLocations = ref(0);
	const selectedIndex = ref(null);
	const selectedItem = ref(null);
	const fieldCssClass = computed(() => (props.class ? props.class : ''));

	const validateCity = useValidateField('city');
	const validateZipcode = useValidateField('zipcode');
	const validateBCity = useValidateField('b_city');
	const validateBZipcode = useValidateField('b_zipcode');

	// get data provided by parent component
	const {setFieldValue, values} = inject('baseFormData');

	// define vee input field
	const {
		value: inputValue,
		errorMessage,
		validate,
		setErrors,
	} = useField(props.name, undefined, {
		initialValue: props.value,
		validateOnValueUpdate: false,
	});

	// set initial selected location, populate related fields and validate
	onMounted(async () => {
		if (props.field.name == 'location' && values?.city && values?.zipcode) {
			selectedItem.value = {zipcode: values.zipcode, title: values.city};
			inputValue.value = values.zipcode + ' ' + values.city;
			validateLocationField();
			await validateCity();
			await validateZipcode();
		}

		if (props.field.name == 'b_location' && values?.b_city && values?.b_zipcode) {
			selectedItem.value = {zipcode: values.b_zipcode, title: values.b_city};
			inputValue.value = values.b_zipcode + ' ' + values.b_city;
			validateLocationField();
			await validateBCity();
			await validateBZipcode();
		}
	});

	// watch input value and set floating label
	const {floatingLabel} = inject('baseFormFieldData', {floatingLabel: false});
	watch(
		() => inputValue.value,
		() => {
			floatingLabel.value = inputValue.value ? true : false;
		}
	);

	// validate field when form is submitted
	const isSubmitting = useIsSubmitting();
	watch(isSubmitting, () => {
		validateLocationField();
	});

	// watch if there is a f_another_country field and reset other fields if it is checked
	const countryValue = useFieldValue('f_another_country');
	watch(countryValue, (fieldValue, oldValue) => {
		if (oldValue != undefined) {
			inputValue.value = '';
			selectedItem.value = null;
			floatingLabel.value = false;
			locations.value = [];
			validateLocationField();
		}
	});

	// set error message if field is empty
	const l = labels.get('error_not_empty');
	function validateLocationField() {
		const err = selectedItem.value ? [] : l;

		// b_location field
		if (props.field.related_field) {
			if (values[props.field.related_field] == false) {
				return setErrors(err);
			} else {
				return setErrors([]);
			}
		}

		// if country field exists, validate only if it is false
		if (countryValue.value != undefined) {
			if (countryValue.value == false) {
				return setErrors(err);
			} else {
				return setErrors([]);
			}
		}

		// if country field does not exist
		return setErrors(err);
	}

	let typingTimer;
	async function handleInput(event) {
		// clear timer on each keypress
		clearTimeout(typingTimer);

		// if value is empty, reset autocomplete container and validate field
		if (!inputValue.value) {
			locations.value = [];
			selectedItem.value = null;
			floatingLabel.value = false;
			selectedIndex.value = null;
			validateLocationField();
		}

		// proceed only if key is letter, number, space, backspace, arrow down, arrow up or enter
		if (!/^[a-zA-Z0-9 ]*$/.test(event.key) && !['Backspace', 'ArrowDown', 'ArrowUp', 'Enter', 'Escape'].includes(event.key)) {
			return false;
		}

		// if arrow down or arrow up key is pressed go to next or previous item and set input value to selected item title
		if (event.key == 'ArrowDown' || event.key == 'ArrowUp') {
			if (event.key == 'ArrowDown') {
				selectedIndex.value = selectedIndex.value == null ? 0 : selectedIndex.value + 1;
				if (selectedIndex.value > totalLocations.value - 1) {
					selectedIndex.value = 0;
				}
				scrollToLocation();
			}

			if (event.key == 'ArrowUp') {
				selectedIndex.value--;
				if (selectedIndex.value < 0) {
					selectedIndex.value = totalLocations.value - 1;
				}
				scrollToLocation();
			}

			selectedItem.value = locations.value[selectedIndex.value];
			inputValue.value = selectedItem.value ? selectedItem.value.zipcode + ' ' + selectedItem.value.title : inputValue.value;
			populateRelatedFields(selectedItem.value.zipcode, selectedItem.value.title);
			return false;
		}

		// if enter key is pressed, select item and stop typing timer
		if (event.key == 'Enter' && selectedItem.value) {
			if (locations.value.length) {
				event.preventDefault();
			}
			await selectLocation(selectedItem.value);
			return false;
		}

		if (event.key == 'Escape') {
			onBlur();
			return false;
		}

		// run search function only if typing has stopped for 300ms
		typingTimer = setTimeout(getLocations, 300);
	}

	// scroll to selected item if it is not visible
	function scrollToLocation() {
		const el = document.querySelector('li[data-location="' + selectedIndex.value + '"]');
		if (el) el.scrollIntoView({block: 'nearest', inline: 'nearest'});
	}

	// get locations from api
	async function getLocations() {
		if (!inputValue.value || inputValue.value.length < 1) return false;

		loading.value = true;
		totalLocations.value = 0;
		const res = await webshop.fetchLocations({q: inputValue.value});
		if (res.data) {
			locations.value = res.data;
			totalLocations.value = res.data.length;
		}
		loading.value = false;
	}

	// select location
	async function selectLocation(location) {
		selectedItem.value = location;
		inputValue.value = location.zipcode + ' ' + location.title;
		selectedIndex.value = null;
		locations.value = [];
		populateRelatedFields(location.zipcode, location.title);
		validateLocationField();

		// Submit customer data to update shipping options and cart (limited shipping options, shipping costs updates, etc.)
		await webshop.submitCustomerData(
			{
				zipcode: location.zipcode,
				city: location.title,
				location: location.zipcode + ' ' + location.title,
				shipping_infos: true,
			},
			{type: 'webshop.customer-location'}
		);
	}

	// set floating label on focus (ignore checkbox or radio fields)
	function onFocus(event) {
		floatingLabel.value = true;
	}

	function onBlur(event) {
		setTimeout(() => {
			// clear field if valid location is not selected
			if (!selectedItem.value || (selectedItem.value && inputValue.value != selectedItem.value.zipcode + ' ' + selectedItem.value.title)) {
				inputValue.value = '';
				selectedItem.value = null;
				populateRelatedFields('', '');
			}
			if (!selectedItem.value) {
				floatingLabel.value = false;
			}

			// if location is selected, hide autocomplete container
			locations.value = [];
			validateLocationField();
		}, 200);
	}

	// update city and zipcode fields and validate
	async function populateRelatedFields(zipcode, city) {
		const fieldPrefix = props.field.name == 'b_location' ? 'b_' : '';
		setFieldValue(fieldPrefix + 'zipcode', zipcode);
		setFieldValue(fieldPrefix + 'city', city);

		if (props.field.name == 'location') {
			await validateCity();
			await validateZipcode();
		}

		if (props.field.name == 'b_location') {
			await validateBCity();
			await validateBZipcode();
		}
	}

	onBeforeUnmount(() => {
		clearTimeout(typingTimer);
		setErrors([]);
	});
</script>

<style lang="less" scoped>
	.autocomplete-container {
		position: absolute;
		left: 0;
		right: 0;
		top: var(--acOffsetTop, 100%);
		display: block;
		font-size: var(--acFontSize, 14px);
		line-height: var(--acLineHeight, 1.5);
		background: var(--acBackgroundColor, #fff);
		z-index: 100;

		border: 1px solid var(--acBorderColor, #ccc);
	}
	ul {
		list-style: none;
		padding: 0 !important;
		margin: 0 !important;
		max-height: var(--acMaxHeight, 200px);
		overflow: auto;
	}
	li {
		padding: var(--acItemPadding, 5px 20px) !important;
		cursor: pointer;
		&:before {
			display: none !important;
		}
		&:hover,
		&.active {
			background: var(--acHoverBackgroundColor, #ccc);
			color: var(--acHoverColor, #000);
		}
	}
</style>
