<template>
	<BaseForm @submit="onSubmit" :loading="loading" v-slot="{errors, meta, values}" v-bind="$attrs">
		<slot :loading="loading" :errors="errors" :meta="meta" :values="values" :fields="fields" :status="status" />
	</BaseForm>
</template>

<script setup>
	const {fetchForm, submitForm} = useSiteform();
	const {sortByField} = useArrayUtils();
	const emit = defineEmits(['success', 'error']);
	const props = defineProps({
		code: {
			type: String,
			default: 'contact',
		},
		fieldsConfig: Object,
	});

	const fields = ref([]);
	const loading = ref(false);
	const status = ref(null);

	let recaptcha;
	if (__RECAPTCHA__) recaptcha = useRecaptcha();

	async function onSubmit({values}) {
		loading.value = true;

		// If recaptcha is enabled, fetch the token and add it to the data
		if (recaptcha) {
			const recaptchaToken = await recaptcha.fetchRecaptchaToken({action: 'submit-form'});
			if (recaptchaToken) values['g-recaptcha-response'] = recaptchaToken;
		}

		const res = await submitForm({type: props.code, values});
		emit(res.success ? 'success' : 'error', res.data);
		status.value = res;
		loading.value = false;
	}

	function formatFields(fields) {
		if (!fields?.length) return;

		// If fieldConfig.order is set, use it to override the default fields order
		if (props.fieldsConfig?.order) fields = sortByField(fields, props.fieldsConfig.order);

		return fields;
	}

	onMounted(async () => {
		const formData = await fetchForm({type: props.code});
		if (!formData.success) {
			useLog(['Form data not found. Check if the form code is correct:', props.code], 'error');
		}
		fields.value = formData.data?.length ? formData.data : [];
		formatFields(fields.value);
	});
</script>
