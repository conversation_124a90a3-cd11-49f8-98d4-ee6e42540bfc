<template>
	<slot :onToggle="toggleAccordion" :active="isActive">
		<div class="accordion-panel" :class="{'active': isActive}">
			<div class="accordion-panel-header" @click="toggleAccordion">
				<slot name="header" />
			</div>
			<div class="accordion-panel-body">
				<slot name="body" />
			</div>
		</div>
	</slot>
</template>

<script setup>
	const props = defineProps({
		active: {
			type: Boolean,
			default: false,
		},
		id: {
			type: [Number, String],
			required: true,
		},
	});

	const {selected, collapsable} = inject('baseAccordionData');
	const isActive = ref(props.active);

	// watch for changes in selected value. If collapsable is true, close accordion if it's not the selected one
	watch(
		() => selected.value,
		() => {
			if (collapsable && props.id != selected.value) isActive.value = false;
		}
	);

	// toggle accordion
	function toggleAccordion() {
		isActive.value = !isActive.value;
		selected.value = collapsable ? props.id : null;
	}

	// expose isActive ref so it can be set from outside
	defineExpose({
		isActive,
	});
</script>

<style scoped>
	.accordion-panel-body {
		overflow: hidden;
		max-height: 0;
		transition: max-height 0.3s ease-in-out;
	}
	.accordion-panel.active .accordion-panel-body {
		max-height: none;
		overflow: auto;
	}
</style>
