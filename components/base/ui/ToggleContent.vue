<template>
	<div ref="contentElement" :class="{'active': active}">
		<slot :content="content" :contentLength="contentLength" :active="active" :onToggle="onToggle" />
	</div>
</template>

<script setup>
	const props = defineProps({
		content: String,
		limit: Number,
		append: Object,
	});

	let contentLength = 0;
	const active = ref(false);
	const content = ref(null);
	const contentElement = ref(null);
	let toggleElement = null;

	// Truncate text and preserve HTML tags
	function truncate(content, maxLength = 255) {
		let container = document.createElement('div');
		container.innerHTML = content;

		let limitReached = false;
		let counted = 0;

		let nodeHandler = node => {
			if (limitReached) {
				node.remove();
				return;
			}

			let childNodes = Array.from(node.childNodes);
			if (childNodes.length) {
				childNodes.forEach(childNode => nodeHandler(childNode));
			} else {
				counted += node.textContent.length;
				if (counted >= maxLength) {
					limitReached = true;
					if (counted > maxLength) {
						node.textContent = node.textContent.slice(0, -(counted - maxLength));
					}
				}
			}
		};

		nodeHandler(container);
		return container.innerHTML;
	}

	// Append HTML to the last word
	function appendToLastWord(htmlContent, appendHtml) {
		// Create a temporary div to parse the HTML
		const tempDiv = document.createElement('div');
		tempDiv.innerHTML = htmlContent;

		// Function to find the last text node and modify it
		function processNode(node) {
			if (node.nodeType === Node.TEXT_NODE && node.nodeValue.trim() !== '') {
				const words = node.nodeValue.trim().split(' ');
				if (words.length > 1) {
					const lastWord = words.pop();
					const span = document.createElement('span');
					span.innerHTML = lastWord + appendHtml;
					const newNode = document.createElement('span');
					newNode.appendChild(document.createTextNode(words.join(' ') + ' '));
					newNode.appendChild(span);
					node.replaceWith(newNode);
				} else {
					const span = document.createElement('span');
					span.innerHTML = words[0] + appendHtml;
					node.replaceWith(span);
				}
				return true;
			} else if (node.nodeType === Node.ELEMENT_NODE) {
				// Process child nodes in reverse order to find the last word
				for (let i = node.childNodes.length - 1; i >= 0; i--) {
					if (processNode(node.childNodes[i])) {
						return true;
					}
				}
			}
			return false;
		}

		// Start processing from the last node
		processNode(tempDiv);

		return tempDiv.innerHTML;
	}

	function setContent() {
		if (props.limit >= contentLength) {
			return props.content;
		}

		let cnt;
		if (!active.value) {
			cnt = truncate(props.content, props.limit);
			if (props.append?.dots != false) cnt = appendToLastWord(cnt, '...');
			if (props.append?.show?.title) cnt = appendToLastWord(cnt, ` <span class="btn-toggle-content">${props.append?.show?.title}</span>`);
		} else {
			cnt = props.content;
			if (props.append?.hide?.title) cnt = appendToLastWord(cnt, ` <span class="btn-toggle-content">${props.append?.hide?.title}</span>`);
		}
		return cnt;
	}

	function attachEventListeners() {
		toggleElement = contentElement.value.querySelector('.btn-toggle-content');
		if (toggleElement) {
			toggleElement.removeEventListener('click', onToggle);
			toggleElement.addEventListener('click', onToggle);
		}
	}

	onMounted(() => {
		contentLength = props.content?.trim().length || 0;
		content.value = setContent();
		if (props.append) nextTick(() => attachEventListeners());
	});

	function onToggle() {
		active.value = !active.value;
		content.value = setContent();
		if (props.append) nextTick(() => attachEventListeners());
	}

	onBeforeUnmount(() => {
		if (toggleElement) toggleElement.removeEventListener('click', onToggle);
	});
</script>
