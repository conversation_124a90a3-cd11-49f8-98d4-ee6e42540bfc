<template>
	<div class="ip fancybox_iframe" :class="{'instashop-gallery': mobileBreakpoint}" @click="instashopOpen(item, index, items)" v-for="(item,index) in items" :key="item.id">
		<span class="ip-overlay"><BaseCmsLabel code="view_buy" tag="span" /></span>
		<span><BaseUiImage :data="item.main_image_thumbs?.['width355-height355-crop1']" alt="" default="/images/no-image-355.jpg" loading="lazy" /></span>
	</div>
</template>

<script setup>
	const {mobileBreakpoint} = inject('rwd');
	const props = defineProps({
		items: {
			type: Object,
			required: true
		}
	})

	const instashopActive = ref(false);
	const instashopItemIndex = ref();
	const instashopItems = ref([]);
	const instashopItemsProducts = ref([]);

	/*
	const instashopData = useState(
		'instashopData', () => ({
			instashopActive: false,
			instashopItemIndex: null,
			items: []
		})
	)
	*/

	const instashopData = useState('instashopData', () => ({
		instashopActive: false,
		instashopItemIndex: null,
		items: [],
		instashopItemsProducts: []
	}))

	function resetInstashopData() {
		instashopData.value = {
			instashopActive: false,
			instashopItemIndex: null,
			items: [],
			instashopItemsProducts: []
		}
	}

	function instashopOpen(item,index,items){
		instashopData.value.instashopActive = false;
		instashopData.value.instashopItemIndex = null;
		instashopData.value.items.splice(0);
		instashopData.value.instashopItemsProducts.splice(0);

		items.forEach(element => {
			const elementId = element.id;
			const elementImages = element?.images?.[0]?.imagemaps || [];
			const idsArray = [];

			if(elementImages?.length){
				elementImages.forEach(instashopPoint => {
					const instashopPointId = instashopPoint.id;
					idsArray.push(instashopPointId);
				});
			}

			element.instashopIds = idsArray;
		});

		instashopData.value.instashopActive = true;
		instashopData.value.instashopItemIndex = index;
		instashopData.value.items.push(...items);
	}
</script>
