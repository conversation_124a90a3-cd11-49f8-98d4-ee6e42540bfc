<template>
	<BaseWebshopAddToCart v-slot="{onAddToCart, loading}" :data="itemsData" :redirect="false" :add-to-cart-modal="false">
		<div class="instashop-bottom">
			<div class="instashop-total-products">
				<BaseCmsLabel code="instashop_total" :replace="[{'%TOTAL_PRODUCTS%': itemsData.length}]" />
				<!--strong><BaseUtilsFormatCurrency :price="total" /></strong-->
			</div>
			<div class="btn btn-orange btn-instashop-add" :class="{'loading': loading, 'disabled': checkedItems?.length == 1}" @click="onAddToCart"><UiLoader v-if="loading" /><BaseCmsLabel code="add_instashop_to_shopping_cart" tag="span" /></div>
		</div>
	</BaseWebshopAddToCart>
</template>

<script setup>
	const props = defineProps(['items']);
	const itemsData = [];

	if(props?.items){
		props.items.forEach(item => {
			if(item?.available_qty > 0){
				console.log(item);
				itemsData.push({modalData: item, quantity: item.qty, shopping_cart_code: item.shopping_cart_code});
			}
		});
	}
</script>
