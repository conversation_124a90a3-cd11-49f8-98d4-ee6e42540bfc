<template>
	<ClientOnly>
		<BaseCatalogWishlist v-slot="{items: wishlistItems}" v-model="wishlistProducts">
			<BaseWebshopAddToCart :data="addToCartwishlistProducts" v-slot="{onAddToCart, loading}">
				<div class="w-wishlist" id="view_wishlist" v-if="wishlistItems.length > 0">
					<div class="w-wishlist-btns w-wishlist-header">
						<h2 class="wishlist-title w-wishlist-title">
							<BaseCmsLabel code="wishlist" />
							<span class="wishlist-title-counter w-title-counter">
								<span class="wishlist_count" :class="{'active': wishlistItems?.length}"> {{wishlistItems?.length}} </span>
							</span>
						</h2>
						<!-- FIXME @mile - potrebno dodati funkciju koja automatski pregledava da li su svi proizvodi iz liste želja u košarici da se makne ovaj gumb -->
						<button :class="['btn btn-white btn-move-to-cart', {'loading': loading}]" @click="onAddToCart">
							<UiLoader v-if="loading" />
							<BaseCmsLabel code="move_all_to_cart" tag="span" />
						</button>
					</div>

					<div id="items_wishlist" class="cart-wishlist-items">
						<template v-for="item in wishlistItems" :key="item.id">
							<CatalogIndexEntryWishlist :item="item" :cartItems="parcels[0].items" mode="cart" list="Lista želja" />
						</template>
					</div>

					<div class="w-wishlist-btns w-wishlist-footer" v-if="wishlistItems?.length > 5">
						<button :class="['btn btn-white btn-move-to-cart', {'loading': loading}]" @click="onAddToCart">
							<UiLoader v-if="loading" />
							<BaseCmsLabel code="move_all_to_cart" tag="span" />
						</button>
					</div>
				</div>
			</BaseWebshopAddToCart>
		</BaseCatalogWishlist>
	</ClientOnly>
</template>

<script setup>
	const props = defineProps(['parcels', 'data']);
	/* function onLoadedProducts(products){
		if(products?.items.length){
			sendProductImpressions(products.items, "wishlist");
		}
	} */

	const wishlistProducts = ref([]);
	const addToCartwishlistProducts = computed (() => {
		if(!wishlistProducts.value.length) return [];
		return wishlistProducts.value.map(item => {
			return{
				modalData: item,
				shopping_cart_code: item.shopping_cart_code,
				quantity: item.qty,
			};
		});
	});
</script>
