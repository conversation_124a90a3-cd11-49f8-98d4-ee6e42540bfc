<template>
	<slot :onSubmit="onSubmit" :loading="loading" :loyalty="loyalty" :newIsActive="newIsActive" :newCartTotal="newCartTotal" :newCartPercent="newCartPercent" :usePointsActive="usePointsActive" />
</template>

<script setup>
	const {getCartData, submitCustomerData} = useWebshop();
	const cartData = computed(() => getCartData());
	const loading = ref(false);

	   const loyalty = computed(() => {
	       return cartData.value?.cart?.loyalty ? cartData.value?.cart?.loyalty : false;
	   });

	const newIsActive = computed(() => {
		return cartData.value?.cart?.loyalty?.use_loyalty_checked ? cartData.value?.cart?.loyalty?.use_loyalty_checked : false;
	});

	   const newCartTotal = computed(() => {
	       return cartData.value?.total?.total_extra_loyalty_new ? cartData.value.total.total_extra_loyalty_new : null;
	   });

	   const newCartPercent = computed(() => {
	       return cartData.value?.total?.total_extra_loyalty_new_discount_percent ? cartData.value.total.total_extra_loyalty_new_discount_percent : null;
	   });

	const usePointsActive = computed(() => {
		return cartData.value?.total?.total_extra_loyalty == cartData.value?.cart?.loyalty?.all_points_discount*-1;
	});

	async function onSubmit(usePoints) {
		if (loading.value) return;
		loading.value = true;

		let body = {loyalty_request_new: !newIsActive.value};

		if(usePoints){
			body = {use_loyalty_point: !usePointsActive.value};
		}

		const res = await submitCustomerData(
			body,
			{type: false}
		);
		loading.value = false;
		return res;
	}
</script>
