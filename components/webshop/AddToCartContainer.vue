<template>
	<div class="add-to-cart-container" :class="{'unavailable': product.is_available, 'cd-qty-limit': product.available_qty <= 1}">
		<template v-if="product.is_available">
			<div class="cd-qty-container" :class="{'wp-qty cd-qty': mode == 'catalogDetail'}" v-show="product.available_qty > 1">
				<BaseThemeWebshopQty :quantity="1" :limit="product.available_qty" v-model="qty" />
				<div class="wp-unit cd-unit" v-if="product?.unit">{{product.unit}}</div>
				<div class="wp-unit cd-unit" v-else><BaseCmsLabel code="unit" /></div>
			</div>
			<BaseWebshopAddToCart v-slot="{onAddToCart, loading}" :data="{modalData: product, shopping_cart_code: product.shopping_cart_code, quantity: qty}">
				<div class="btn btn-orange cd-btn-add noarrow" :class="{'loading': loading}" @click="onAddToCart"><UiLoader v-if="loading" /><BaseCmsLabel code="add_to_shopping_cart" tag="span" /></div>
			</BaseWebshopAddToCart>
		</template>
		<template v-else>
			<FeedbackNotificationForm status="not-available" :item="product" />
		</template>
	</div>
</template>

<script setup>
	const props = defineProps({
		item: Object,
		mode: String
	})

	const product = computed(() => props?.item)
	const qty = ref();
</script>

<style lang="less" scoped>
	:deep(.qty-input-container){height: 100%;}
</style>
