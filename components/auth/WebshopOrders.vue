<template>
	<template v-if="orders?.length">
		<div class="w-table w-table-head">
			<BaseCmsLabel code="webshop_order_number" class="w-table-col col-num" tag="div" />
			<BaseCmsLabel code="date" class="w-table-col col-date" tag="div" />
			<BaseCmsLabel code="amount" class="w-table-col col-total" tag="div" />
			<BaseCmsLabel code="status" class="w-table-col col-status" tag="div" />
			<div class="w-table-col col-btns"></div>
		</div>
		<div class="orders">
			<BaseUiAccordion>
				<BaseUiAccordionPanel v-for="item in visibleOrders" :key="item.id" :id="item.id" v-slot="{onToggle, active}">
					<div class="order-row" :class="{'active': active, 'active': mode == 'dashboard'}" id="order-{{item.number}}">
						<!-- Order Item -->
						<div class="table-order" @click="onToggle">
							<div class="w-table-col col-num"><BaseCmsLabel class="w-table-label" tag="span" code="webshop_order_number" /> {{item.id}}</div>
							<div class="w-table-col col-date"><BaseCmsLabel class="w-table-label" tag="span" code="date" /> {{item.date}}</div>
							<div class="w-table-col col-total">
								<BaseCmsLabel class="w-table-label" tag="span" code="amount" />:
								<span><BaseUtilsFormatCurrency :price="item.total" /></span>
							</div>
							<div class="w-table-col col-status col-td-status">
								<BaseCmsLabel code="status" />: {{item.status.title}}
								<div class="order-status">
									<span class="order-status-bar" :style="getStatusStyle(item.status)"></span>
								</div>
							</div>
							<div class="w-table-col col-btns" v-if="mode != 'dashboard'">
								<div class="btn-order-details" id="btn-order">
									<span class="btn-inactive" v-if="!active"><BaseCmsLabel code="webshop_btn_order_details" /></span>
									<span class="btn-active" v-else><BaseCmsLabel code="webshop_btn_hide_order_details" /></span>
									<span class="toggle-icon"></span>
								</div>
							</div>
						</div>
						<div v-if="item.order_items" v-show="active" class="order-details">
							<div class="w-table w-table-details">
								<AuthOrderItem v-for="product in item.order_items" :product="product" :key="product.id" />
							</div>
							<NuxtLink v-if="item?.satisfaction_survey_allowed && item?.satisfaction_survey_url" :to="item.satisfaction_survey_url" class="btn btn-primary btn-satisfaction">
								<span><BaseCmsLabel code="profile_satisfaction_btn" /></span>
							</NuxtLink>
						</div>
					</div>
				</BaseUiAccordionPanel>
			</BaseUiAccordion>
		</div>
	</template>
	<BaseCmsLabel code="no_orders" class="auth-no-orders" tag="div" v-else />
</template>

<script setup>
	const props = defineProps(['orders','mode']);
	const visibleOrders = computed(() => {
		return props.mode === 'dashboard' ? props.orders.slice(0, 5) : props.orders;
	});

	const itemsData = computed(() => {
		if(props.mode === 'dashboard') {
			return props.items ? [props.items[0]] : null
		} else {
			return props.items ? props.items : null
		}
	});

	function getStatusStyle(status) {
		let width = '30%';
		let color = 'var(--colorBase)';

		if (status.id === '10' || status.id === '13') {
			width = '30%';
		}
		if (status.id === '16' || status.id === '19') {
			width = '60%';
			color = 'var(--colorBaseBlue)';
		}
		if (status.id === '22' || status.id === '25' || status.id === '28') {
			width = '100%';
			color = 'var(--colorRed)';
		}
		if (status.id === '22') {
			color = 'var(--colorGreen)';
		}

		return `width: ${width}; background: ${color};`;
	}
</script>

<style lang="less" scoped>
	.btn-satisfaction{height: 50px; min-width: 120px; margin: 20px 0;}
</style>
