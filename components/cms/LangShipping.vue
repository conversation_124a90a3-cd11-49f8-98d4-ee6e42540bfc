<template>
	<ClientOnly>
		<Body :class="{'body-country-select': countrySelect}" />
		<div class="w-lang" :class="{'active': langActive}" ref="langShipping">
			<BaseCmsLanguages v-slot="{items, currentLanguage}">
				<div class="w-lang-span" @click="langActive = !langActive">
					<div class="btn-w-toggle">
						<span :class="'flag '+currentLanguage"></span><span>{{currentLanguage.lang}}</span>
					</div>
					<div class="delivery-to"><BaseCmsLabel code="shipping_country_select" /> <span class="bold2">Hrvatska</span></div>
				</div>

				<div class="w-list">
					<div class="ww-preview-close w-lang-mobile-close" @click="langActive = !langActive"></div>
					<div class="w-list-lang">
						<a v-for="item in items" :key="item.code" :class="[item.code, currentLanguage.code == item.code && 'active']" :href="item.url"><span :class="'flag '+item.code"></span>{{item.code}}</a>
					</div>
					<!-- FIXME INTEG integrirti odabir države za slanje -->
					<div class="w-delivery-change-cnt">
						<div><BaseCmsLabel code="shipping_country_select" /> <span class="bold2">Hrvatska</span></div>
						<BaseCmsLabel code="change_shipping_country_btr" tag="div" class="w-delivery-change" @click="countrySelectOpen()" />
						<!--
						<div><?php echo Arr::get($cmslabel, 'shipping_country_select'); ?> <span class="bold2" data-selected_country><?php echo Arr::get($selected_country, 'title', ''); ?></span></div>
						<a class="w-delivery-change" href="javascript:openCountrySelectModal()"><?php echo Arr::get($cmslabel, 'change_shipping_country_btr'); ?></a>
						-->
					</div>
				</div>
			</BaseCmsLanguages>
		</div>
		<WebshopShippingCountrySelect>
			<template #closeCountrySelect>
				<div class="fancybox-item fancybox-close shipping-country-close" @click="countrySelect = !countrySelect"></div>
			</template>
		</WebshopShippingCountrySelect>
	</ClientOnly>
</template>

<script setup>
	const {onClickOutside} = useDom();
	const langActive = ref(false);
	const countrySelect = ref(false);
	const langShipping = ref(null);

	function countrySelectOpen(){
		langActive.value = false;
		countrySelect.value = true;
	}
	onClickOutside(langShipping, () => {
		langActive.value = false;
	});
</script>

<style lang="less" scoped>
	.shipping-country-close{position: absolute; top: -18px; right: -18px;}
</style>
