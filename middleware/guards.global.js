export default defineNuxtRouteMiddleware(async to => {
	const nuxtApp = useNuxtApp();
	if (process.server || nuxtApp.$isbot) return;

	const config = useAppConfig();
	const auth = useAuth();
	const webshop = useWebshop();
	const {getLastUrlSegment, getUrlSegments} = useUrl();
	const appRoutes = useApiRoutes();
	const isLoggedIn = computed(() => auth.isLoggedIn());
	const lastUrlSegment = getLastUrlSegment(to.path);
	const secondUrlSegment = getUrlSegments(to.path, {ignoreLang: true})[1];

	// redirect after logout
	if (!config.keycloak && to.meta.contentType == 'auth' && lastUrlSegment == 'logout') {
		await auth.logout();
		if (to.query?.redirect) {
			return (window.location.href = to.query.redirect);
		}
	}

	// if user is logged in
	if (isLoggedIn.value && config.auth.loggedInRedirect) {
		if (['login', 'signup', 'forgotten_password', 'confirm_signup', 'signup_confirm'].includes(secondUrlSegment) || ['AuthDefault'].includes(to.meta.template)) {
			const redirectUrl = appRoutes.getAppUrl(config.auth.loggedInRedirect);
			if (to.path != redirectUrl) {
				return (window.location.href = redirectUrl);
			}
		}

		// redirect user from default dashboard page if dashboard is set in config
		if (config?.auth?.dashboard && to.meta.template == 'AuthDefault') {
			return (window.location.href = appRoutes.getAppUrl(config.auth.dashboard));
		}
	}

	// if user is logged out
	if (!isLoggedIn.value && config.auth.loggedOutRedirect) {
		if (to.meta.contentType == 'auth' && !['login', 'signup', 'forgotten_password', 'confirm_signup', 'signup_confirm'].includes(secondUrlSegment)) {
			return (window.location.href = appRoutes.getAppUrl(config.auth.loggedOutRedirect));
		}
	}

	// redirect to cart if cart is empty and trying to access checkout
	if (['login', 'customer', 'shipping', 'payment', 'review_order'].includes(to.meta.action)) {
		const cartItems = await webshop.fetchIsCartEmpty();
		if (cartItems?.success) return (window.location.href = appRoutes.getAppUrl('webshop_shopping_cart'));
	}

	// redirect to checkout login if trying to access checkout and user is not logged in nor guest
	if (['customer', 'shipping', 'payment', 'review_order'].includes(to.meta.action)) {
		const isLoggedIn = await auth.fetchIsLogin();
		if (!isLoggedIn?.continue_as_guest_exist && !isLoggedIn?.user_id) {
			return (window.location.href = appRoutes.getAppUrl('webshop_login'));
		}
	}
});
